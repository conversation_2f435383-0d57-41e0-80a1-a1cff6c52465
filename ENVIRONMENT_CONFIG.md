# 🌍 Environment Configuration Guide

This project supports multiple environments for Lamboo API endpoints. You can easily switch between production and development environments.

## 📋 Available Environments

### Production Environment
- **URL**: `https://api.lamboo.finance`
- **Description**: Live production API with real data
- **Use case**: Normal data collection and analysis

### Development Environment  
- **URL**: `https://api.dev.cashdrop.click`
- **Description**: Development/testing API
- **Use case**: Testing new features or development work

## 🔧 How to Switch Environments

### Method 1: Edit tokenList.js (Recommended)

1. Open `tokenList.js` file
2. Find the environment configuration section at the top:

```javascript
// === ENVIRONMENT CONFIGURATION ===
// Switch between production and development environments
const LAMBOO_API_BASE_URL = {
  production: 'https://api.lamboo.finance',
  development: 'https://api.dev.cashdrop.click'
};

// Current environment - change this to switch between environments
const CURRENT_ENV = 'production'; // 'production' or 'development'
```

3. Change `CURRENT_ENV` value:
   - For production: `const CURRENT_ENV = 'production';`
   - For development: `const CURRENT_ENV = 'development';`

4. Save the file

### Method 2: Add New Environment

You can add new environments by modifying the `LAMBOO_API_BASE_URL` object:

```javascript
const LAMBOO_API_BASE_URL = {
  production: 'https://api.lamboo.finance',
  development: 'https://api.dev.cashdrop.click',
  staging: 'https://api.staging.example.com',  // Add new environment
  local: 'http://localhost:3000'               // Add local environment
};

const CURRENT_ENV = 'staging'; // Use new environment
```

## 📊 Affected Scripts

The following scripts use the environment configuration:

### ✅ Scripts that use Lamboo API (affected by environment change):
- `collectors/LambooMarketData.js` - Pool data collection
- `collectors/lambooTokenData.js` - Token metrics collection

### ❌ Scripts that don't use Lamboo API (not affected):
- `collectors/dexscreenerData.js` - Uses Dexscreener API
- `collectors/GeckoTerminalData.js` - Uses GeckoTerminal API  
- `collectors/OKXMetricData.js` - Uses OKX API
- All comparison scripts - Only process collected data

## 🚀 Usage Examples

### Example 1: Switch to Development for Testing
```bash
# 1. Edit tokenList.js: set CURRENT_ENV = 'development'
# 2. Run data collection
node collectors/LambooMarketData.js
node collectors/lambooTokenData.js

# You'll see: 🌍 Using development environment: https://api.dev.cashdrop.click
```

### Example 2: Switch to Production for Real Data
```bash
# 1. Edit tokenList.js: set CURRENT_ENV = 'production'  
# 2. Run data collection
node collectors/LambooMarketData.js
node collectors/lambooTokenData.js

# You'll see: 🌍 Using production environment: https://api.lamboo.finance
```

### Example 3: Run Full Pipeline with Specific Environment
```bash
# 1. Set environment in tokenList.js
# 2. Run complete pipeline
node runAll.js

# The environment message will appear at the start of each Lamboo script
```

## 🔍 Environment Verification

When you run any script that uses Lamboo API, you'll see a message indicating which environment is being used:

```
🌍 Using production environment: https://api.lamboo.finance
```

or

```
🌍 Using development environment: https://api.dev.cashdrop.click
```

This helps you verify that you're using the correct environment.

## ⚠️ Important Notes

1. **Data Differences**: Production and development environments may have different data
2. **Rate Limits**: Different environments may have different rate limiting policies
3. **Availability**: Development environment may not always be available
4. **Backup**: Always backup your data before switching environments for important work
5. **Consistency**: Make sure all team members use the same environment for consistent results

## 🛠️ Troubleshooting

### Issue: "Environment not found" error
**Solution**: Check that `CURRENT_ENV` matches one of the keys in `LAMBOO_API_BASE_URL`

### Issue: API calls failing after environment switch
**Solution**: 
1. Verify the new environment URL is accessible
2. Check if the API endpoints are the same across environments
3. Ensure you have proper access to the target environment

### Issue: Different data between environments
**Solution**: This is expected - development and production environments contain different datasets

## 📝 Configuration File Structure

```
tokenList.js
├── ENVIRONMENT CONFIGURATION
│   ├── LAMBOO_API_BASE_URL (object with environment URLs)
│   ├── CURRENT_ENV (current environment selector)
│   └── API_BASE_URL (computed current URL)
└── TOKEN DATA
    └── tokenData (array of tokens to process)
```
