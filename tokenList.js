// === ENVIRONMENT CONFIGURATION ===
// Switch between production and development environments
const LAMBOO_API_BASE_URL = {
  production: 'https://api.lamboo.finance',
  development: 'https://api.dev.cashdrop.click'
};

// Current environment - change this to switch between environments
const CURRENT_ENV = 'production'; // 'production' or 'development'

// Export the current API base URL
const API_BASE_URL = LAMBOO_API_BASE_URL[CURRENT_ENV];

console.log(`🌍 Using ${CURRENT_ENV} environment: ${API_BASE_URL}`);

// === TOKEN DATA ===
const tokenData = [
  // {
  //   "name": "USDC",
  //   "address": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b",
  //   "symbol": "USDC"
  // },
  // {
  //   "name": "Tether USD",
  //   "address": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b",
  //   "symbol": "USDt"
  // },
  // {
  //   "name": "Aptos Coin",
  //   "address": "0x1::aptos_coin::AptosCoin",
  //   "symbol": "APT"
  // },
  // {
  //   "name": "OKX Wrapped BTC",
  //   "address": "0x81214a80d82035a190fcb76b6ff3c0145161c3a9f33d137f2bbaee4cfec8a387",
  //   "symbol": "xBTC"
  // },
  // {
  //   "name": "Staked USDe",
  //   "address": "0xb30a694a344edee467d9f82330bbe7c3b89f440a1ecd2da1f3bca266560fce69",
  //   "symbol": "sUSDe"
  // },
  // {
  //   "name": "Wrapped BTC",
  //   "address": "0x68844a0d7f2587e726ad0579f3d640865bb4162c08a4589eeda3f9689ec52a3d",
  //   "symbol": "WBTC"
  // },
  {
    "name": "Amnis Aptos Coin",
    "address": "0x111ae3e5bc816a5e63c2da97d0aa3886519e0cd5e4b046659fa35796bd11542a::amapt_token::AmnisApt",
    "symbol": "amAPT"
  },
  {
    "name": "Amnis Aptos Coin",
    "address": "0xa259be733b6a759909f92815927fa213904df6540519568692caf0b068fe8e62",
    "symbol": "amAPT"
  },
  {
    "name": "Echo",
    "address": "0xb2c7780f0a255a6137e5b39733f5a4c85fe093c549de5c359c1232deef57d1b7",
    "symbol": "ECHO"
  },
  {
    "name": "AMNIS",
    "address": "0xb36527754eb54d7ff55daf13bcb54b42b88ec484bd6f0e3b2e0d1db169de6451",
    "symbol": "AMI"
  },
  // {
  //   "name": "Hyperion",
  //   "address": "0x435ad41e7b383cef98899c4e5a22c8dc88ab67b22f95e5663d6c6649298c3a9d",
  //   "symbol": "RION"
  // },
  // {
  //   "name": "Universal BTC",
  //   "address": "0xf764dbfd6999067ac052a8e722ae359bec389bd7dba19ead586801b99b81b075",
  //   "symbol": "uniBTC"
  // },
  // {
  //   "name": "Staked Aptos Coin",
  //   "address": "0xb614bfdf9edc39b330bbf9c3c5bcd0473eee2f6d4e21748629cc367869ece627",
  //   "symbol": "stAPT"
  // },
  // {
  //   "name": "AURO Finance",
  //   "address": "0xbcff91abababee684b194219ff2113c26e63d57c8872e6fdaf25a41a45fb7197",
  //   "symbol": "AURO"
  // },
  // {
  //   "name": "USD Coin (LayerZero)",
  //   "address": "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDC",
  //   "symbol": "lzUSDC"
  // },
  // {
  //   "name": "Fiamma BTC",
  //   "address": "0x75de592a7e62e6224d13763c392190fda8635ebb79c798a5e9dd0840102f3f93",
  //   "symbol": "FIABTC"
  // },
  // {
  //   "name": "Staked Kofi APT",
  //   "address": "0x42556039b88593e768c97ab1a3ab0c6a17230825769304482dff8fdebe4c002b",
  //   "symbol": "stkAPT"
  // },
  // {
  //   "name": "aBTC (Echo)",
  //   "address": "0xf599112bc3a5b6092469890d6a2f353f485a6193c9d36626b480704467d3f4c8",
  //   "symbol": "aBTC"
  // },
  // {
  //   "name": "Kofi APT",
  //   "address": "0x821c94e69bc7ca058c913b7b5e6b0a5c9fd1523d58723a966fb8c1f5ea888105",
  //   "symbol": "kAPT"
  // },
  // {
  //   "name": "Move Dollar",
  //   "address": "0x6f986d146e4a90b828d8c12c14b6f4e003fdff11a8eecceceb63744363eaac01::mod_coin::MOD",
  //   "symbol": "MOD"
  // },
  // {
  //   "name": "🚀💯 emojicoin",
  //   "address": "0x40af260f7394ef56d6222322fff4bfa70922bb8369f7ee92c1c1ae697260b7d6",
  //   "symbol": "🚀💯"
  // },
  // {
  //   "name": "AURO USDA",
  //   "address": "0x534e4c3dc0f038dab1a8259e89301c4da58779a5d482fb354a41c08147e6b9ec",
  //   "symbol": "USDA"
  // },
  // {
  //   "name": "Move Dollar",
  //   "address": "0x94ed76d3d66cb0b6e7a3ab81acf830e3a50b8ae3cfb9edc0abea635a11185ff4",
  //   "symbol": "MOD"
  // },
  // {
  //   "name": "aBTC (Echo)",
  //   "address": "0x4e1854f6d332c9525e258fb6e66f84b6af8aba687bbcb832a24768c4e175feec::abtc::ABTC",
  //   "symbol": "aBTC"
  // },
  // {
  //   "name": "🦁♥️ emojicoin",
  //   "address": "0x01932f35165dd0a9038961d5c145ea90544106de39f3f4011a4c681eab249a99",
  //   "symbol": "🦁♥️"
  // },
  // {
  //   "name": "Thala Token",
  //   "address": "0x7fd500c11216f0fe3095d0c4b8aa4d64a4e2e04f83758462f2b127255643615::thl_coin::THL",
  //   "symbol": "THL"
  // },
  // {
  //   "name": "♋ emojicoin",
  //   "address": "0xf1a46b61a8737fdea75d7b9cfcf7c2e78d80aaa0cf69ecfc400704d8d9adbbc8",
  //   "symbol": "♋"
  // },
  // {
  //   "name": "Tether USD (LayerZero)",
  //   "address": "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDT",
  //   "symbol": "lzUSDT"
  // },
  // {
  //   "name": "Thala APT",
  //   "address": "0xfaf4e633ae9eb31366c9ca24214231760926576c7b625313b3688b5e900731f6::staking::ThalaAPT",
  //   "symbol": "thAPT"
  // },
  // {
  //   "name": "Staked Aptos Coin",
  //   "address": "0x111ae3e5bc816a5e63c2da97d0aa3886519e0cd5e4b046659fa35796bd11542a::stapt_token::StakedApt",
  //   "symbol": "stAPT"
  // }
];

// Export for use in other files
module.exports = {
  tokenData,
  API_BASE_URL,
  CURRENT_ENV
};