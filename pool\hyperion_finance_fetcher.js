#!/usr/bin/env node

/**
 * Hyperion Finance API Data Fetcher
 * 
 * <PERSON><PERSON><PERSON> để fetch pool data từ Hyperion Finance GraphQL API
 * và lưu response vào file JSON với error handling và logging đầy đủ.
 */

const https = require('https');
const fs = require('fs').promises;
const path = require('path');

class Logger {
    constructor() {
        this.logFile = 'hyperion_finance_fetcher.log';
    }

    async log(level, message) {
        const timestamp = new Date().toISOString();
        const logMessage = `${timestamp} - ${level} - ${message}\n`;
        
        // Log to console
        console.log(`${timestamp} - ${level} - ${message}`);
        
        // Log to file
        try {
            await fs.appendFile(this.logFile, logMessage);
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    async info(message) {
        await this.log('INFO', message);
    }

    async error(message) {
        await this.log('ERROR', message);
    }

    async warning(message) {
        await this.log('WARNING', message);
    }
}

class HyperionFinanceAPI {
    constructor() {
        this.apiUrl = 'https://api.hyperion.xyz/v1/graphql';
        this.poolId = '0xce131db3fb4f52c4a1526e2cf1d8a5383a6c5c0beaa610c277856a8721a9b5a3';
        this.headers = {
            'accept': 'application/graphql-response+json, application/json',
            'accept-language': 'en,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'origin': 'https://hyperion.xyz',
            'priority': 'u=1, i',
            'referer': 'https://hyperion.xyz/',
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36'
        };
        this.logger = new Logger();
    }

    /**
     * Tạo GraphQL query payload
     * @param {string} poolId - Pool ID để query (optional)
     * @returns {Object} GraphQL payload
     */
    buildGraphQLPayload(poolId = null) {
        const query = `
  query queryPoolById($poolId: String = "") {
    api{
      getPoolStat(poolId: $poolId) {
        
  dripAmount
  dailyVolumeUSD
  totalVolumeUSD
  feesUSD
  tvlUSD
  feeAPR
  farmAPR
  pool {
    
  currentTick
  feeRate
  feeTier
  poolId
  senderAddress
  sqrtPrice
  token1
  token2
  dripLpBase
  dripLpMultiples
  dripTradeBase
  dripTradeMultiples
  token1Info {
    
  assetType
  bridge
  coinMarketcapId
  coinType
  coingeckoId
  decimals
  faType
  hyperfluidSymbol
  logoUrl
  name
  symbol
  tags
  isBanned
  websiteUrl
  }
  token2Info {
    
  assetType
  bridge
  coinMarketcapId
  coinType
  coingeckoId
  decimals
  faType
  hyperfluidSymbol
  logoUrl
  name
  symbol
  tags
  isBanned
  websiteUrl 
  }
  farm {
    poolId
    rewardFa
    emissionsPerSecond
  }
 
  }
 
      }
    }
  }
`;

        return {
            query: query,
            variables: {
                poolId: poolId || this.poolId
            }
        };
    }

    /**
     * Fetch pool data từ Hyperion Finance GraphQL API
     * @param {string} poolId - Pool ID để query (optional)
     * @param {number} timeout - Request timeout in milliseconds
     * @returns {Promise<Object>} API response data
     */
    async fetchPoolData(poolId = null, timeout = 30000) {
        return new Promise((resolve, reject) => {
            const payload = this.buildGraphQLPayload(poolId);
            const postData = JSON.stringify(payload);
            
            this.logger.info(`Đang fetch data từ: ${this.apiUrl}`);
            this.logger.info(`Pool ID: ${poolId || this.poolId}`);

            const options = {
                method: 'POST',
                headers: {
                    ...this.headers,
                    'Content-Length': Buffer.byteLength(postData)
                },
                timeout: timeout
            };

            const req = https.request(this.apiUrl, options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        if (res.statusCode !== 200) {
                            throw new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`);
                        }

                        const jsonData = JSON.parse(data);
                        
                        // Check for GraphQL errors
                        if (jsonData.errors && jsonData.errors.length > 0) {
                            const errorMessages = jsonData.errors.map(err => err.message).join(', ');
                            throw new Error(`GraphQL errors: ${errorMessages}`);
                        }

                        this.logger.info('Fetch pool data thành công');

                        // Log basic info về response
                        if (jsonData.data && jsonData.data.api && jsonData.data.api.getPoolStat) {
                            const poolStats = jsonData.data.api.getPoolStat;
                            if (Array.isArray(poolStats) && poolStats.length > 0) {
                                const poolStat = poolStats[0];
                                this.logger.info(`TVL: $${parseFloat(poolStat.tvlUSD)?.toLocaleString() || 'N/A'}`);
                                this.logger.info(`Daily Volume: $${parseFloat(poolStat.dailyVolumeUSD)?.toLocaleString() || 'N/A'}`);
                                this.logger.info(`Total Volume: $${parseFloat(poolStat.totalVolumeUSD)?.toLocaleString() || 'N/A'}`);
                                this.logger.info(`Fees: $${parseFloat(poolStat.feesUSD)?.toLocaleString() || 'N/A'}`);
                                this.logger.info(`Fee APR: ${(parseFloat(poolStat.feeAPR) * 100)?.toFixed(2) || 'N/A'}%`);
                                this.logger.info(`Farm APR: ${(parseFloat(poolStat.farmAPR) * 100)?.toFixed(2) || 'N/A'}%`);
                            }
                        }

                        resolve(jsonData);
                    } catch (error) {
                        this.logger.error(`Lỗi parse JSON response: ${error.message}`);
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                this.logger.error(`Request error: ${error.message}`);
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                const error = new Error(`Request timeout sau ${timeout}ms`);
                this.logger.error(error.message);
                reject(error);
            });

            req.setTimeout(timeout);
            req.write(postData);
            req.end();
        });
    }

    /**
     * Lưu data vào file JSON
     * @param {Object} data - Data cần lưu
     * @param {string} filename - Tên file (optional)
     * @returns {Promise<boolean>} Success status
     */
    async saveToFile(data, filename = null) {
        try {
            if (!filename) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                filename = `hyperion_finance_data_${timestamp}.json`;
            }

            // Tạo outputs directory nếu chưa có
            const outputDir = 'outputs';
            try {
                await fs.access(outputDir);
            } catch {
                await fs.mkdir(outputDir, { recursive: true });
                this.logger.info(`Đã tạo thư mục: ${outputDir}`);
            }

            const filepath = path.join(outputDir, filename);
            await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf8');

            this.logger.info(`Data đã được lưu vào: ${filepath}`);
            return true;
        } catch (error) {
            this.logger.error(`Lỗi khi lưu file: ${error.message}`);
            return false;
        }
    }

    /**
     * Trích xuất key metrics từ GraphQL API response
     * @param {Object} data - Full GraphQL API response
     * @returns {Object} Key metrics
     */
    extractKeyMetrics(data) {
        try {
            if (!data.data || !data.data.api || !data.data.api.getPoolStat) {
                throw new Error('Invalid GraphQL response structure');
            }

            const poolStats = data.data.api.getPoolStat;
            if (!Array.isArray(poolStats) || poolStats.length === 0) {
                throw new Error('No pool stats found in response');
            }

            const poolStat = poolStats[0];
            const pool = poolStat.pool || {};
            const token1Info = pool.token1Info || {};
            const token2Info = pool.token2Info || {};
            const farm = pool.farm || {};

            const metrics = {
                timestamp: new Date().toISOString(),
                pool_id: pool.poolId || this.poolId,
                tvl_usd: parseFloat(poolStat.tvlUSD) || 0,
                daily_volume_usd: parseFloat(poolStat.dailyVolumeUSD) || 0,
                total_volume_usd: parseFloat(poolStat.totalVolumeUSD) || 0,
                fees_usd: parseFloat(poolStat.feesUSD) || 0,
                fee_apr: parseFloat(poolStat.feeAPR) || 0,
                farm_apr: parseFloat(poolStat.farmAPR) || 0,
                total_apr: (parseFloat(poolStat.feeAPR) || 0) + (parseFloat(poolStat.farmAPR) || 0),
                drip_amount: parseFloat(poolStat.dripAmount) || 0,
                pool_details: {
                    current_tick: pool.currentTick,
                    fee_rate: parseFloat(pool.feeRate) || 0,
                    fee_tier: pool.feeTier,
                    sender_address: pool.senderAddress,
                    sqrt_price: pool.sqrtPrice,
                    token1: pool.token1,
                    token2: pool.token2,
                    drip_lp_base: parseFloat(pool.dripLpBase) || 0,
                    drip_lp_multiples: parseFloat(pool.dripLpMultiples) || 0,
                    drip_trade_base: parseFloat(pool.dripTradeBase) || 0,
                    drip_trade_multiples: parseFloat(pool.dripTradeMultiples) || 0
                },
                token1_info: {
                    symbol: token1Info.symbol,
                    name: token1Info.name,
                    asset_type: token1Info.assetType,
                    coin_type: token1Info.coinType,
                    decimals: token1Info.decimals,
                    logo_url: token1Info.logoUrl,
                    coingecko_id: token1Info.coingeckoId,
                    coinmarketcap_id: token1Info.coinMarketcapId,
                    website_url: token1Info.websiteUrl,
                    is_banned: token1Info.isBanned,
                    tags: token1Info.tags
                },
                token2_info: {
                    symbol: token2Info.symbol,
                    name: token2Info.name,
                    asset_type: token2Info.assetType,
                    coin_type: token2Info.coinType,
                    decimals: token2Info.decimals,
                    logo_url: token2Info.logoUrl,
                    coingecko_id: token2Info.coingeckoId,
                    coinmarketcap_id: token2Info.coinMarketcapId,
                    website_url: token2Info.websiteUrl,
                    is_banned: token2Info.isBanned,
                    tags: token2Info.tags
                },
                farm_info: {
                    pool_id: farm.poolId,
                    reward_fa: farm.rewardFa,
                    emissions_per_second: parseFloat(farm.emissionsPerSecond) || 0
                },
                query_pool_id: this.poolId
            };

            return metrics;
        } catch (error) {
            this.logger.error(`Lỗi khi trích xuất key metrics: ${error.message}`);
            return {};
        }
    }

    /**
     * Log chi tiết về pool data
     * @param {Object} data - GraphQL API response data
     */
    async logPoolDetails(data) {
        try {
            if (!data.data || !data.data.api || !data.data.api.getPoolStat) {
                await this.logger.warning('Không tìm thấy pool data trong response');
                return;
            }

            const poolStats = data.data.api.getPoolStat;
            if (!Array.isArray(poolStats) || poolStats.length === 0) {
                await this.logger.warning('Không có pool stats trong response');
                return;
            }

            const poolStat = poolStats[0];
            const pool = poolStat.pool || {};
            const token1Info = pool.token1Info || {};
            const token2Info = pool.token2Info || {};
            const farm = pool.farm || {};

            await this.logger.info('=== HYPERION FINANCE POOL DETAILS ===');
            await this.logger.info(`Pool ID: ${pool.poolId || 'N/A'}`);
            await this.logger.info(`Fee Tier: ${pool.feeTier || 'N/A'}`);
            await this.logger.info(`Fee Rate: ${(parseFloat(pool.feeRate) * 100)?.toFixed(4) || 'N/A'}%`);
            await this.logger.info(`Current Tick: ${pool.currentTick || 'N/A'}`);
            await this.logger.info(`Sqrt Price: ${pool.sqrtPrice || 'N/A'}`);

            // Financial metrics
            await this.logger.info('\n--- Financial Metrics ---');
            await this.logger.info(`TVL: $${parseFloat(poolStat.tvlUSD)?.toLocaleString() || 'N/A'}`);
            await this.logger.info(`Daily Volume: $${parseFloat(poolStat.dailyVolumeUSD)?.toLocaleString() || 'N/A'}`);
            await this.logger.info(`Total Volume: $${parseFloat(poolStat.totalVolumeUSD)?.toLocaleString() || 'N/A'}`);
            await this.logger.info(`Fees Collected: $${parseFloat(poolStat.feesUSD)?.toLocaleString() || 'N/A'}`);
            await this.logger.info(`Drip Amount: ${parseFloat(poolStat.dripAmount)?.toLocaleString() || 'N/A'}`);

            // APR information
            await this.logger.info('\n--- APR Information ---');
            const feeAPR = (parseFloat(poolStat.feeAPR) * 100) || 0;
            const farmAPR = (parseFloat(poolStat.farmAPR) * 100) || 0;
            const totalAPR = feeAPR + farmAPR;
            await this.logger.info(`Fee APR: ${feeAPR.toFixed(2)}%`);
            await this.logger.info(`Farm APR: ${farmAPR.toFixed(2)}%`);
            await this.logger.info(`Total APR: ${totalAPR.toFixed(2)}%`);

            // Token information
            await this.logger.info('\n--- Token 1 Information ---');
            await this.logger.info(`Symbol: ${token1Info.symbol || 'N/A'}`);
            await this.logger.info(`Name: ${token1Info.name || 'N/A'}`);
            await this.logger.info(`Decimals: ${token1Info.decimals || 'N/A'}`);
            await this.logger.info(`Asset Type: ${token1Info.assetType || 'N/A'}`);
            await this.logger.info(`Coingecko ID: ${token1Info.coingeckoId || 'N/A'}`);

            await this.logger.info('\n--- Token 2 Information ---');
            await this.logger.info(`Symbol: ${token2Info.symbol || 'N/A'}`);
            await this.logger.info(`Name: ${token2Info.name || 'N/A'}`);
            await this.logger.info(`Decimals: ${token2Info.decimals || 'N/A'}`);
            await this.logger.info(`Asset Type: ${token2Info.assetType || 'N/A'}`);
            await this.logger.info(`Coingecko ID: ${token2Info.coingeckoId || 'N/A'}`);

            // Farm information
            if (farm.poolId) {
                await this.logger.info('\n--- Farm Information ---');
                await this.logger.info(`Farm Pool ID: ${farm.poolId}`);
                await this.logger.info(`Reward FA: ${farm.rewardFa || 'N/A'}`);
                await this.logger.info(`Emissions Per Second: ${parseFloat(farm.emissionsPerSecond)?.toLocaleString() || 'N/A'}`);
            }

            // Drip information
            await this.logger.info('\n--- Drip Information ---');
            await this.logger.info(`LP Base: ${parseFloat(pool.dripLpBase)?.toLocaleString() || 'N/A'}`);
            await this.logger.info(`LP Multiples: ${parseFloat(pool.dripLpMultiples)?.toLocaleString() || 'N/A'}`);
            await this.logger.info(`Trade Base: ${parseFloat(pool.dripTradeBase)?.toLocaleString() || 'N/A'}`);
            await this.logger.info(`Trade Multiples: ${parseFloat(pool.dripTradeMultiples)?.toLocaleString() || 'N/A'}`);

            await this.logger.info('\n==========================================');
        } catch (error) {
            await this.logger.error(`Lỗi khi log pool details: ${error.message}`);
        }
    }

    /**
     * Set pool ID để query
     * @param {string} poolId - Pool ID mới
     */
    setPoolId(poolId) {
        this.poolId = poolId;
        this.logger.info(`Đã set Pool ID: ${poolId}`);
    }
}

/**
 * Main function
 */
async function main() {
    const api = new HyperionFinanceAPI();

    try {
        await api.logger.info('🚀 Bắt đầu fetch Hyperion Finance data');

        // Fetch data từ GraphQL API
        const data = await api.fetchPoolData();

        // Log chi tiết về pool
        await api.logPoolDetails(data);

        // Lưu complete response
        const success = await api.saveToFile(data);

        if (success) {
            // Trích xuất và lưu key metrics
            const metrics = api.extractKeyMetrics(data);
            if (Object.keys(metrics).length > 0) {
                await api.logger.info('✅ Key metrics đã được trích xuất thành công');

                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                const metricsFilename = `hyperion_finance_metrics_${timestamp}.json`;
                await api.saveToFile(metrics, metricsFilename);
            }
        }

        await api.logger.info('🎉 Hoàn thành fetch Hyperion Finance data');
        process.exit(0);

    } catch (error) {
        await api.logger.error(`❌ Lỗi khi fetch data: ${error.message}`);
        process.exit(1);
    }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
    const logger = new Logger();
    await logger.error(`Unhandled Rejection: ${reason}`);
    process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
    const logger = new Logger();
    await logger.error(`Uncaught Exception: ${error.message}`);
    process.exit(1);
});

// Chạy main function nếu script được execute trực tiếp
if (require.main === module) {
    main();
}

module.exports = { HyperionFinanceAPI };
