{"name": "dataaptos", "version": "1.0.0", "description": "```\r tests/dataAptos/\r ├── 📂 collectors/          # Data collection scripts\r │   ├── dexscreenerData.js     # Collect from Dexscreener API\r │   ├── LambooMarketData.js    # Collect pool data from Lamboo API\r │   ├── lambooTokenData.js     # Collect token metrics from Lamboo API\r │   ├── OKXMetricData.js       # Collect trading data from OKX API\r │   └── GeckoTerminalData.js   # Collect pool data from GeckoTerminal API\r │\r ├── 📂 comparisons/         # Data comparison scripts\r │   ├── compareVsDexscreener.js # Compare Dexscreener vs Lamboo pools\r │   ├── compareVsOKX.js        # Compare OKX vs Lamboo token metrics\r │   └── compareVsGeckoTerminal.js # Compare GeckoTerminal vs Lamboo pools\r │\r ├── 📂 results/             # Raw JSON data files\r │   ├── dexscreenerData.json\r │   ├── LambooMarketData.json\r │   ├── lambooTokenData.json\r │   ├── OKXMetricData.json\r │   ├── GeckoTerminalData.json\r │   ├── compareVsDexscreener.json\r │   ├── compareVsOKX.json\r │   └── compareVsGeckoTerminal.json\r │\r ├── 📂 reports/             # Human-readable reports\r │   ├── compareVsDexscreener.csv\r │   ├── compareVsOKX.csv\r │   ├── compareVsGeckoTerminal.csv\r │   └── compareVsGeckoTerminal.html  # 🎨 Colored HTML report\r │\r └── tokenList.js            # Central token list (shared by all scripts)\r ```", "main": "runAll.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/JayC304/dataAptos.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/JayC304/dataAptos/issues"}, "homepage": "https://github.com/JayC304/dataAptos#readme", "dependencies": {"axios": "^1.12.2"}}