# Thala Finance Data Fetcher

Script JavaScript để fetch dữ liệu TVL (Total Value Locked) và volume từ Thala Finance API.

## C<PERSON>ch sử dụng

### Chạy script
```bash
node thala_finance_fetcher.js
```

### Yêu cầu
- Node.js (<PERSON><PERSON><PERSON><PERSON> bản 12 trở lên)
- Kết nối internet

## Dữ liệu được fetch

Script sẽ lấy các thông tin sau từ Thala Finance API:

- **TVL**: Total Value Locked
- **Volume 24h**: Khối lượng giao dịch 24 giờ
- **Fees 24h**: <PERSON><PERSON> giao dịch 24 giờ  
- **Balances**: Số dư các token trong pool
- **APR Sources**: <PERSON>ác nguồn APR và tỷ lệ
- **Pool Metadata**: Thông tin cấu hình pool

## Output Files

Script tạo ra các file trong thư mục `outputs/`:

1. **Complete Data**: `thala_finance_data_YYYY-MM-DDTHH-MM-SS.json`
   - <PERSON><PERSON><PERSON> to<PERSON><PERSON> bộ response từ API

2. **Key Metrics**: `thala_finance_metrics_YYYY-MM-DDTHH-MM-SS.json`
   - Chứa các metrics quan trọng đã được trích xuất

3. **Log File**: `thala_finance_fetcher.log`
   - Log chi tiết quá trình thực thi

## Ví dụ Output

### Key Metrics
```json
{
  "timestamp": "2025-09-22T10:10:39.383Z",
  "tvl": 13606952.052399263,
  "volume_24h": 2169248.858897039,
  "fees_24h": 542.3127156561638,
  "balances": [6129882.504437, 6253300.462138],
  "apr_sources": [
    {"source": "thAPT", "apr": 0.06838218888212953},
    {"source": "Swap Fees", "apr": 0.014547279982484913},
    {"source": "Ethena Staking APR", "apr": 0.04235180729658818}
  ],
  "pool_type": "Metastable",
  "swap_fee": 0.0005,
  "amp": 200
}
```

## Error Handling

Script bao gồm xử lý lỗi cho:
- Lỗi kết nối mạng
- Lỗi HTTP (4xx, 5xx)
- Lỗi parse JSON
- Lỗi ghi file
- Timeout

## Logging

Tất cả hoạt động được log với timestamp và level:
- ✅ Thành công
- ❌ Lỗi
- ℹ️ Thông tin

## API Endpoint

```
https://app.thala.fi/api/liquidity-pool?pool-type=0xce9e3b2437fd2cddc5c14f6c4259fc7d3cef160b820837591aa48170bb509368
```

## Tự động hóa

Để chạy script định kỳ, bạn có thể sử dụng:

### Windows Task Scheduler
Tạo task để chạy script theo lịch trình

### Cron (Linux/macOS)
```bash
# Chạy mỗi giờ
0 * * * * cd /path/to/script && node thala_finance_fetcher.js

# Chạy mỗi 15 phút
*/15 * * * * cd /path/to/script && node thala_finance_fetcher.js
```
