# DeFi Data Fetchers

Collection các script JavaScript để fetch dữ liệu từ các DeFi protocol trên Aptos.

## Available Scripts

### 1. Thala Finance Fetcher (`thala_finance_fetcher.js`)
Fetch dữ liệu TVL và volume từ Thala Finance API.

```bash
node thala_finance_fetcher.js
```

### 2. Cellana Finance Fetcher (`cellana_finance_fetcher.js`)
Fetch dữ liệu pools từ Cellana Finance API.

```bash
node cellana_finance_fetcher.js
```

### 3. Hyperion Finance Fetcher (`hyperion_finance_fetcher.js`)
Fetch dữ liệu pool từ Hyperion Finance GraphQL API.

```bash
node hyperion_finance_fetcher.js
```

### Yêu cầu
- Node.js (phiên bản 12 trở lên)
- Kết nối internet

## Dữ liệu được fetch

### Thala Finance
- **TVL**: Total Value Locked
- **Volume 24h**: Khối lượng giao dịch 24 giờ
- **Fees 24h**: <PERSON><PERSON> giao dịch 24 giờ
- **Balances**: <PERSON>ố dư các token trong pool
- **APR Sources**: Các nguồn APR và tỷ lệ
- **Pool Metadata**: Thông tin cấu hình pool

### Cellana Finance
- **Pool Information**: Tên, symbol, address của pool
- **TVL**: Total Value Locked của từng pool
- **APR**: Annual Percentage Rate
- **Token Details**: Thông tin chi tiết về các token trong pool
- **Reserves**: Số lượng token trong pool
- **Gauge Information**: Thông tin về voting và rewards
- **Staking Data**: Dữ liệu staking

### Hyperion Finance
- **Pool Statistics**: TVL, volume, fees từ GraphQL API
- **APR Information**: Fee APR và Farm APR
- **Token Information**: Chi tiết về token pairs (APT/amAPT)
- **Pool Details**: Current tick, fee rate, sqrt price
- **Farm Data**: Emissions và reward information
- **Drip Information**: LP và trade multipliers

## Output Files

Mỗi script tạo ra các file trong thư mục `outputs/`:

### Thala Finance
1. **Complete Data**: `thala_finance_data_YYYY-MM-DDTHH-MM-SS.json`
2. **Key Metrics**: `thala_finance_metrics_YYYY-MM-DDTHH-MM-SS.json`
3. **Log File**: `thala_finance_fetcher.log`

### Cellana Finance
1. **Complete Data**: `cellana_finance_data_YYYY-MM-DDTHH-MM-SS.json`
2. **Key Metrics**: `cellana_finance_metrics_YYYY-MM-DDTHH-MM-SS.json`
3. **Log File**: `cellana_finance_fetcher.log`

### Hyperion Finance
1. **Complete Data**: `hyperion_finance_data_YYYY-MM-DDTHH-MM-SS.json`
2. **Key Metrics**: `hyperion_finance_metrics_YYYY-MM-DDTHH-MM-SS.json`
3. **Log File**: `hyperion_finance_fetcher.log`

## Ví dụ Output

### Thala Finance Key Metrics
```json
{
  "timestamp": "2025-09-22T10:10:39.383Z",
  "tvl": 13606952.052399263,
  "volume_24h": 2169248.858897039,
  "fees_24h": 542.3127156561638,
  "balances": [6129882.504437, 6253300.462138],
  "apr_sources": [
    {"source": "thAPT", "apr": 0.06838218888212953},
    {"source": "Swap Fees", "apr": 0.014547279982484913},
    {"source": "Ethena Staking APR", "apr": 0.04235180729658818}
  ],
  "pool_type": "Metastable",
  "swap_fee": 0.0005,
  "amp": 200
}
```

### Cellana Finance Key Metrics
```json
{
  "timestamp": "2025-09-22T10:17:08.289Z",
  "total_pools": 1,
  "total_tvl_from_api": 9489122.206287704,
  "pools_tvl_sum": 208.7415641454791,
  "pools": [
    {
      "pool_name": "LOON/APT",
      "pool_address": "0x6c2b2c3d5140ee973622ea3fa75a86d0706551864a33aa0feac06bdfc788841b",
      "tvl": 208.7415641454791,
      "apr": 0.022449402806688182,
      "stable": false,
      "token0": {"symbol": "LOON", "name": "The Loonies"},
      "token1": {"symbol": "APT", "name": "Aptos Coin"},
      "gauge": {"status": "ACTIVE", "apr": 0.3029848811930654}
    }
  ]
}
```

### Hyperion Finance Key Metrics
```json
{
  "timestamp": "2025-09-22T10:24:48.333Z",
  "pool_id": "0xce131db3fb4f52c4a1526e2cf1d8a5383a6c5c0beaa610c277856a8721a9b5a3",
  "tvl_usd": 12736873.199053694,
  "daily_volume_usd": 398280.72050471546,
  "total_volume_usd": 75348866.76947652,
  "fees_usd": 39.82887951722925,
  "fee_apr": 0.2120245713236472,
  "farm_apr": 0,
  "total_apr": 0.2120245713236472,
  "token1_info": {
    "symbol": "APT",
    "name": "Aptos Coin",
    "coingecko_id": "aptos"
  },
  "token2_info": {
    "symbol": "amAPT",
    "name": "Amnis Aptos Coin",
    "coingecko_id": "amnis-aptos"
  }
}
```

## Error Handling

Script bao gồm xử lý lỗi cho:
- Lỗi kết nối mạng
- Lỗi HTTP (4xx, 5xx)
- Lỗi parse JSON
- Lỗi ghi file
- Timeout

## Logging

Tất cả hoạt động được log với timestamp và level:
- ✅ Thành công
- ❌ Lỗi
- ℹ️ Thông tin

## API Endpoints

### Thala Finance
```
https://app.thala.fi/api/liquidity-pool?pool-type=0xce9e3b2437fd2cddc5c14f6c4259fc7d3cef160b820837591aa48170bb509368
```

### Cellana Finance
```
https://api-v2.cellana.finance/api/v1/pool/0x0?address=0x0&page=1&limit=10&poolType=ALL_POOLS&search=0x6c2b2c3d5140ee973622ea3fa75a86d0706551864a33aa0feac06bdfc788841b&votingRewards=&isVotePage=0&=null
```

### Hyperion Finance (GraphQL)
```
https://api.hyperion.xyz/v1/graphql
```
**Query**: `queryPoolById` với poolId parameter

## Tự động hóa

Để chạy script định kỳ, bạn có thể sử dụng:

### Windows Task Scheduler
Tạo task để chạy script theo lịch trình

### Cron (Linux/macOS)
```bash
# Chạy Thala Finance mỗi giờ
0 * * * * cd /path/to/script && node thala_finance_fetcher.js

# Chạy Cellana Finance mỗi 30 phút
*/30 * * * * cd /path/to/script && node cellana_finance_fetcher.js

# Chạy Hyperion Finance mỗi giờ
0 * * * * cd /path/to/script && node hyperion_finance_fetcher.js

# Chạy tất cả mỗi 15 phút
*/15 * * * * cd /path/to/script && node thala_finance_fetcher.js && node cellana_finance_fetcher.js && node hyperion_finance_fetcher.js
```
