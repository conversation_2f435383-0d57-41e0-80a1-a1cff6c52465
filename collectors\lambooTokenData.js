/**
 * Fetch token metrics from Lamboo API for tokens in tokenList.js
 * Simple one-command script: just run `node lambooTokenData.js`
 * 
 * Gets token addresses from tokenList.js and fetches metrics data.
 * API: {API_BASE_URL}/metrics/token/{address}?forceCalc=true
 * Output: address, symbol, data for 5m, 1h, 24h timeframes
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Import token list and API configuration
const { tokenData, API_BASE_URL, CURRENT_ENV } = require('../tokenList.js');

// === CONFIG ===
const CONFIG = {
  MAX_TOKENS: 30, // Maximum tokens to process (set to -1 for all tokens)
  OUT_FILE: '../results/lambooTokenData.json',
  DELAY_BETWEEN_REQUESTS: 1000, // ms delay between API calls (Lamboo API might be rate limited)
  TIMEOUT: 30000, // Request timeout in ms
};

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

async function fetchTokenMetrics(tokenAddress, tokenInfo) {
  const url = `${API_BASE_URL}/metrics/token/${encodeURIComponent(tokenAddress)}?forceCalc=true`;
  
  const headers = {
    'accept': 'application/json',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  };

  try {
    console.log(`  Đang gọi API: ${url}`);
    const res = await axios.get(url, { headers, timeout: CONFIG.TIMEOUT });
    
    if (!res.data || !res.data.response) {
      console.log(`  Không có dữ liệu response cho token ${tokenInfo.symbol}`);
      return null;
    }

    const response = res.data.response;
    
    // Extract data for 5m, 1h, 24h timeframes
    const result = {
      address: tokenAddress,
      symbol: tokenInfo.symbol,
      name: tokenInfo.name,
      data: {
        '5m': {
          txns: response.txns5m || 0,
          markers: response.markers5m || 0,
          buys: response.buys5m || 0,
          sells: response.sells5m || 0,
          buyers: response.buyers5m || 0,
          sellers: response.sellers5m || 0,
          volume: response.volume5m || 0,
          volumeBuy: response.volumeBuy5m || 0,
          volumeSell: response.volumeSell5m || 0,
          priceChange: response.priceChange5m || 0,
          holdersChange: response.holdersChange5m || 0,
          liquidityChange: response.liquidityChange5m || 0
        },
        '1h': {
          txns: response.txns1h || 0,
          markers: response.markers1h || 0,
          buys: response.buys1h || 0,
          sells: response.sells1h || 0,
          buyers: response.buyers1h || 0,
          sellers: response.sellers1h || 0,
          volume: response.volume1h || 0,
          volumeBuy: response.volumeBuy1h || 0,
          volumeSell: response.volumeSell1h || 0,
          priceChange: response.priceChange1h || 0,
          holdersChange: response.holdersChange1h || 0,
          liquidityChange: response.liquidityChange1h || 0
        },
        '24h': {
          txns: response.txns24h || 0,
          markers: response.markers24h || 0,
          buys: response.buys24h || 0,
          sells: response.sells24h || 0,
          buyers: response.buyers24h || 0,
          sellers: response.sellers24h || 0,
          volume: response.volume24h || 0,
          volumeBuy: response.volumeBuy24h || 0,
          volumeSell: response.volumeSell24h || 0,
          priceChange: response.priceChange24h || 0,
          holdersChange: response.holdersChange24h || 0,
          liquidityChange: response.liquidityChange24h || 0
        }
      },
      // Additional useful info
      currentPrice: response.price || 0,
      priceNative: response.priceNative || 0,
      liquidity: response.liquidity || 0,
      pairs: response.pairs || 0,
      holders: response.holders || 0,
      lastTxnTime: response.lastTxnTime || null,
      updatedAt: response.updatedAt || null
    };

    return result;
    
  } catch (err) {
    const status = err?.response?.status;
    const message = err?.response?.data?.message || err.message;
    console.error(`  Lỗi khi lấy dữ liệu cho token ${tokenInfo.symbol} (${status}): ${message}`);
    return null;
  }
}

async function main() {
  console.log(`Đang xử lý ${tokenData.length} token từ tokenList.js...`);
  
  const tokensToProcess = CONFIG.MAX_TOKENS === -1 ? tokenData : tokenData.slice(0, CONFIG.MAX_TOKENS);
  console.log(`Sẽ xử lý ${tokensToProcess.length} token đầu tiên.`);

  const allResults = [];
  
  for (let tokenIndex = 0; tokenIndex < tokensToProcess.length; tokenIndex++) {
    const token = tokensToProcess[tokenIndex];
    console.log(`\n[${tokenIndex + 1}/${tokensToProcess.length}] Xử lý token: ${token.symbol} (${token.name})`);
    console.log(`Token address: ${token.address}`);
    
    try {
      const metrics = await fetchTokenMetrics(token.address, token);
      
      if (metrics) {
        console.log(`  ✅ Thành công - Volume 24h: $${metrics.data['24h'].volume.toLocaleString()}`);
        allResults.push(metrics);
      } else {
        console.log(`  ❌ Không có dữ liệu cho token ${token.symbol}`);
      }
      
    } catch (error) {
      console.error(`  ❌ Lỗi khi xử lý token ${token.symbol}:`, error.message);
    }
    
    // Delay between tokens to avoid rate limiting
    if (tokenIndex < tokensToProcess.length - 1) {
      console.log(`  Đợi ${CONFIG.DELAY_BETWEEN_REQUESTS}ms...`);
      await sleep(CONFIG.DELAY_BETWEEN_REQUESTS);
    }
  }

  // Save results
  const outputPath = path.resolve(__dirname, CONFIG.OUT_FILE);
  fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2));
  console.log(`\n✅ Đã lưu ${allResults.length} record vào ${outputPath}`);
  
  // Summary by token
  console.log('\n📊 Tóm tắt kết quả:');
  allResults.forEach(record => {
    const vol24h = record.data['24h'].volume;
    const txns24h = record.data['24h'].txns;
    const priceChange24h = (record.data['24h'].priceChange * 100).toFixed(2);
    console.log(`  ${record.symbol}: Vol24h=$${vol24h.toLocaleString()}, Txns24h=${txns24h.toLocaleString()}, Price24h=${priceChange24h}%`);
  });

  // Summary statistics
  if (allResults.length > 0) {
    const totalVolume24h = allResults.reduce((sum, r) => sum + r.data['24h'].volume, 0);
    const totalTxns24h = allResults.reduce((sum, r) => sum + r.data['24h'].txns, 0);
    const avgPrice24h = allResults.reduce((sum, r) => sum + r.data['24h'].priceChange, 0) / allResults.length * 100;
    
    console.log('\n📈 Thống kê tổng hợp:');
    console.log(`  Tổng Volume 24h: $${totalVolume24h.toLocaleString()}`);
    console.log(`  Tổng Transactions 24h: ${totalTxns24h.toLocaleString()}`);
    console.log(`  Trung bình Price Change 24h: ${avgPrice24h.toFixed(2)}%`);
  }
}

main();
