/**
 * 🚀 Run All Data Collection & Comparison Scripts
 * 
 * This script runs all collectors and comparisons in the correct order.
 * Usage: node runAll.js [--collectors-only] [--comparisons-only]
 */

const { spawn } = require('child_process');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const collectorsOnly = args.includes('--collectors-only');
const comparisonsOnly = args.includes('--comparisons-only');

// Define script sequences
const collectors = [
  { name: 'Dexscreener Data', script: 'collectors/dexscreenerData.js' },
  { name: 'Lamboo Market Data', script: 'collectors/LambooMarketData.js' },
  { name: 'Lamboo Token Data', script: 'collectors/lambooTokenData.js' },
  { name: 'OKX Metric Data', script: 'collectors/OKXMetricData.js' },
  { name: 'GeckoTerminal Data', script: 'collectors/GeckoTerminalData.js' }
];

const comparisons = [
  { name: 'Compare Dexscreener vs Lamb<PERSON>', script: 'comparisons/compareVsDexscreener.js' },
  { name: 'Compare OKX vs Lamboo', script: 'comparisons/compareVsOKX.js' },
  { name: 'Compare GeckoTerminal vs Lamboo', script: 'comparisons/compareVsGeckoTerminal.js' }
];

// Helper function to run a script
function runScript(scriptPath, scriptName) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔄 Running: ${scriptName}`);
    console.log(`📁 Script: ${scriptPath}`);
    console.log('─'.repeat(60));
    
    const child = spawn('node', [scriptPath], {
      cwd: __dirname,
      stdio: 'inherit'
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ Completed: ${scriptName}`);
        resolve();
      } else {
        console.error(`❌ Failed: ${scriptName} (exit code: ${code})`);
        reject(new Error(`Script failed with exit code ${code}`));
      }
    });
    
    child.on('error', (err) => {
      console.error(`❌ Error running ${scriptName}:`, err.message);
      reject(err);
    });
  });
}

// Helper function to run scripts in sequence
async function runSequence(scripts, title) {
  console.log(`\n🎯 ${title}`);
  console.log('='.repeat(60));
  
  for (let i = 0; i < scripts.length; i++) {
    const script = scripts[i];
    try {
      await runScript(script.script, script.name);
      
      // Add delay between scripts to avoid API rate limits
      if (i < scripts.length - 1) {
        console.log('\n⏳ Waiting 2 seconds before next script...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.error(`\n💥 Stopping execution due to error in: ${script.name}`);
      throw error;
    }
  }
}

// Main execution function
async function main() {
  const startTime = Date.now();
  
  console.log('🚀 Dex3 Token Data Collection & Comparison System');
  console.log('='.repeat(60));
  console.log(`📅 Started at: ${new Date().toLocaleString()}`);
  
  try {
    // Run collectors if not comparisons-only
    if (!comparisonsOnly) {
      await runSequence(collectors, 'DATA COLLECTION PHASE');
    }
    
    // Run comparisons if not collectors-only
    if (!collectorsOnly) {
      await runSequence(comparisons, 'DATA COMPARISON PHASE');
    }
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);
    
    console.log('\n🎉 ALL SCRIPTS COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(60));
    console.log(`⏱️  Total execution time: ${duration} seconds`);
    console.log(`📅 Completed at: ${new Date().toLocaleString()}`);
    
    console.log('\n📊 Generated Files:');
    console.log('├── 📂 results/     - JSON data files');
    console.log('├── 📂 reports/     - CSV & HTML reports');
    console.log('└── 🎨 HTML report  - reports/compareVsGeckoTerminal.html');
    
    console.log('\n💡 Next Steps:');
    console.log('• Open reports/compareVsGeckoTerminal.html in browser for visual analysis');
    console.log('• Import CSV files into Excel for detailed analysis');
    console.log('• Use JSON files for programmatic data processing');
    
  } catch (error) {
    console.error('\n💥 EXECUTION FAILED!');
    console.error('Error:', error.message);
    process.exit(1);
  }
}

// Show usage if help requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
🚀 Dex3 Data Collection & Comparison System

Usage:
  node runAll.js                 # Run all collectors and comparisons
  node runAll.js --collectors-only    # Run only data collectors
  node runAll.js --comparisons-only   # Run only data comparisons
  node runAll.js --help              # Show this help

Collectors (Data Collection):
  • dexscreenerData.js      - Collect from Dexscreener API
  • LambooMarketData.js     - Collect pool data from Lamboo API
  • lambooTokenData.js      - Collect token metrics from Lamboo API
  • OKXMetricData.js        - Collect trading data from OKX API
  • GeckoTerminalData.js    - Collect pool data from GeckoTerminal API

Comparisons (Data Analysis):
  • compareVsDexscreener.js - Compare Dexscreener vs Lamboo pools
  • compareVsOKX.js         - Compare OKX vs Lamboo token metrics
  • compareVsGeckoTerminal.js - Compare GeckoTerminal vs Lamboo pools

Output:
  • results/    - Raw JSON data files
  • reports/    - CSV & HTML reports for analysis
`);
  process.exit(0);
}

// Run the main function
main();
