/**
 * OHLCV Comparison - 1-Second Timeframe
 * Lamboo V2 API vs OKX API
 *
 * Timeframe: 1-second candlesticks
 * Analysis window: Most recent 5 minutes, rounded down to nearest minute boundary
 * Expected data: ~300 candles (5 minutes × 60 seconds)
 *
 * Usage: node ohlcv/scripts/compare1s.js
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { tokenData } = require('../../tokenList');

// =============== DEBUG CONFIGURATION ===============
const DEBUG_MODE = process.env.OHLCV_DEBUG === 'true' || process.argv.includes('--debug');

function logDebug(message) {
  if (DEBUG_MODE) {
    console.log(`🐛 [DEBUG] ${message}`);
  }
}

function generateCurlCommand(url, config) {
  if (!DEBUG_MODE) return;

  let curlCmd = `curl -X ${config.method || 'GET'} "${url}"`;

  // Add headers
  if (config.headers) {
    Object.entries(config.headers).forEach(([key, value]) => {
      curlCmd += ` \\\n  -H "${key}: ${value}"`;
    });
  }

  // Add query parameters
  if (config.params) {
    const urlObj = new URL(url);
    Object.entries(config.params).forEach(([key, value]) => {
      urlObj.searchParams.append(key, value);
    });
    curlCmd = curlCmd.replace(`"${url}"`, `"${urlObj.toString()}"`);
  }

  // Add request body
  if (config.data) {
    const bodyStr = typeof config.data === 'string' ? config.data : JSON.stringify(config.data, null, 2);
    curlCmd += ` \\\n  -d '${bodyStr}'`;
  }

  return curlCmd;
}

function logApiRequest(apiName, url, config) {
  if (!DEBUG_MODE) return;

  console.log(`\n🔍 [${apiName}] API Request Debug:`);
  console.log('=' .repeat(80));

  // Log request details
  console.log(`📡 Method: ${config.method || 'GET'}`);
  console.log(`🌐 URL: ${url}`);

  if (config.params) {
    console.log(`🔗 Query Params:`, JSON.stringify(config.params, null, 2));
  }

  if (config.headers) {
    console.log(`📋 Headers:`, JSON.stringify(config.headers, null, 2));
  }

  if (config.data) {
    console.log(`📦 Request Body:`, JSON.stringify(config.data, null, 2));
  }

  // Generate and log cURL command
  const curlCmd = generateCurlCommand(url, config);
  console.log(`\n💻 cURL Command:`);
  console.log(curlCmd);
  console.log('=' .repeat(80));
}

// =============== HELPER FUNCTIONS ===============
const toNum = (x) => (x === null || x === undefined || x === '' ? NaN : Number(x));
const isFiniteNum = (x) => Number.isFinite(toNum(x));

function fmtPrice(x) {
  const n = toNum(x);
  return isFiniteNum(n) ? n.toFixed(10) : "0.0000000000";
}

function toSec(ts) {
  const n = toNum(ts);
  if (!isFiniteNum(n)) return 0;
  return n >= 1e12 ? Math.floor(n / 1000) : Math.floor(n);
}

function calculatePercentageDifference(value1, value2) {
  if (value1 === 0 && value2 === 0) return 0;
  if (value1 === 0) return value2 > 0 ? 100 : -100;
  return ((value2 - value1) / Math.abs(value1)) * 100;
}

function arrayMapper(rows) {
  if (!Array.isArray(rows)) return [];
  return rows.map((r) => {
    if (!Array.isArray(r) || r.length < 5) return null;
    const ts = toSec(r[0]);
    const o = fmtPrice(r[1]);
    const h = fmtPrice(r[2]);
    const l = fmtPrice(r[3]);
    const c = fmtPrice(r[4]);
    const v = r.length >= 6 && isFiniteNum(r[5]) ? Number(r[5]) : 0;
    return [ts, o, h, l, c, v];
  }).filter(Boolean);
}

function parseRowsFromAPI(json, apiName = 'API') {
  if (DEBUG_MODE) {
    logDebug(`${apiName} Response Structure Analysis:`);
    logDebug(`Response type: ${typeof json}`);
    logDebug(`Response keys: ${json ? Object.keys(json).join(', ') : 'null'}`);

    if (json && typeof json === 'object') {
      if (json.data) {
        logDebug(`json.data type: ${typeof json.data}`);
        logDebug(`json.data keys: ${json.data ? Object.keys(json.data).join(', ') : 'null'}`);
        if (json.data.candles) {
          logDebug(`json.data.candles type: ${typeof json.data.candles}`);
          logDebug(`json.data.candles length: ${Array.isArray(json.data.candles) ? json.data.candles.length : 'not array'}`);
        }
        if (json.data.rows) {
          logDebug(`json.data.rows type: ${typeof json.data.rows}`);
          logDebug(`json.data.rows length: ${Array.isArray(json.data.rows) ? json.data.rows.length : 'not array'}`);
        }
      }

      // Log first few characters of response for structure analysis
      const jsonStr = JSON.stringify(json);
      logDebug(`Response preview (first 500 chars): ${jsonStr.substring(0, 500)}...`);
    }
  }

  if (!json || typeof json !== 'object') {
    if (DEBUG_MODE) logDebug(`${apiName} Parse failed: Invalid JSON object`);
    return [];
  }

  if (json.data && Array.isArray(json.data.candles)) {
    if (DEBUG_MODE) logDebug(`${apiName} Using json.data.candles path, found ${json.data.candles.length} candles`);
    return arrayMapper(json.data.candles);
  }

  if (json.data && Array.isArray(json.data.rows)) {
    if (DEBUG_MODE) logDebug(`${apiName} Using json.data.rows path, found ${json.data.rows.length} rows`);
    return arrayMapper(json.data.rows);
  }

  if (Array.isArray(json.candles)) {
    if (DEBUG_MODE) logDebug(`${apiName} Using json.candles path, found ${json.candles.length} candles`);
    return arrayMapper(json.candles);
  }

  if (Array.isArray(json.rows)) {
    if (DEBUG_MODE) logDebug(`${apiName} Using json.rows path, found ${json.rows.length} rows`);
    return arrayMapper(json.rows);
  }

  if (Array.isArray(json)) {
    if (DEBUG_MODE) logDebug(`${apiName} Using direct array path, found ${json.length} items`);
    return arrayMapper(json);
  }

  if (DEBUG_MODE) logDebug(`${apiName} Parse failed: No recognized data structure found`);
  return [];
}

async function httpRequest(url, config, apiName = 'API') {
  try {
    logApiRequest(apiName, url, config);

    const response = await axios(config);

    if (DEBUG_MODE) {
      logDebug(`${apiName} Response Status: ${response.status}`);
      logDebug(`${apiName} Response Data Length: ${JSON.stringify(response.data).length} characters`);
    }

    return response.data;
  } catch (error) {
    if (DEBUG_MODE) {
      logDebug(`${apiName} Request Failed: ${error.message}`);
      if (error.response) {
        logDebug(`${apiName} Error Response Status: ${error.response.status}`);
        logDebug(`${apiName} Error Response Data: ${JSON.stringify(error.response.data)}`);
      }
    }

    if (error.response) {
      throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`);
    } else if (error.request) {
      throw new Error('Network error: No response received');
    } else {
      throw new Error(`Request error: ${error.message}`);
    }
  }
}

function formatNumber(num) {
  if (num === null || num === undefined || !Number.isFinite(num)) {
    return 'N/A';
  }
  return num.toLocaleString(undefined, { maximumFractionDigits: 6 });
}

function formatPercentage(pct) {
  if (pct === null || pct === undefined || !Number.isFinite(pct)) {
    return 'N/A';
  }
  return pct.toFixed(4) + '%';
}

function getStatusColor(avgPriceDiff, avgVolDiff) {
  const priceDiff = Math.abs(avgPriceDiff || 0);
  const volDiff = Math.abs(avgVolDiff || 0);
  
  if (priceDiff < 1 && volDiff < 50) return '#d4edda'; // Green - Excellent
  if (priceDiff < 5 && volDiff < 100) return '#d1ecf1'; // Blue - Good
  if (priceDiff < 10 && volDiff < 200) return '#fff3cd'; // Yellow - Fair
  return '#f8d7da'; // Red - Poor
}

function getStatusText(avgPriceDiff, avgVolDiff) {
  const priceDiff = Math.abs(avgPriceDiff || 0);
  const volDiff = Math.abs(avgVolDiff || 0);
  
  if (priceDiff < 1 && volDiff < 50) return 'EXCELLENT';
  if (priceDiff < 5 && volDiff < 100) return 'GOOD';
  if (priceDiff < 10 && volDiff < 200) return 'FAIR';
  return 'POOR';
}

function formatUTCTimestamp(unixTimestamp) {
  const date = new Date(unixTimestamp * 1000);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const seconds = date.getUTCSeconds().toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const year = date.getUTCFullYear();
  
  return `${hours}:${minutes}:${seconds} ${day}/${month}/${year} UTC`;
}

function formatReadableTimestamp(isoString) {
  const date = new Date(isoString);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const seconds = date.getUTCSeconds().toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  
  return `${hours}:${minutes}:${seconds} ${day}/${month}`;
}

// =============== TIME CALCULATION FUNCTIONS ===============
function calculateRoundedTimeWindow5Minutes() {
  const nowMs = Date.now();
  const nowDate = new Date(nowMs);

  // Round down to nearest minute boundary
  const roundedEndDate = new Date(nowDate.getFullYear(), nowDate.getMonth(), nowDate.getDate(),
    nowDate.getHours(), nowDate.getMinutes(), 0, 0);
  const endTime = Math.floor(roundedEndDate.getTime() / 1000);

  // Calculate start time as exactly 5 minutes before
  const startTime = endTime - (5 * 60); // 5 minutes = 300 seconds

  return { startTime, endTime };
}

// =============== DISCREPANCY TABLE GENERATION ===============
function generateDiscrepancyTable(discrepancies) {
  if (!discrepancies || discrepancies.length === 0) {
    return '<p><em>Không có chênh lệch đáng kể (>5%) được phát hiện.</em></p>';
  }

  let html = `
    <h4>📊 Chi tiết chênh lệch đáng kể (>5% giá OHLC) - Top ${Math.min(discrepancies.length, 20)}</h4>
    <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px;">
      <table style="font-size: 0.9em;">
        <thead>
          <tr>
            <th>Thời gian</th>
            <th>OKX Data<br><small>[timestamp, open, high, low, close, volume]</small></th>
            <th>Lamboo V2 Data<br><small>[timestamp, open, high, low, close, volume]</small></th>
            <th>Open Δ%</th>
            <th>High Δ%</th>
            <th>Low Δ%</th>
            <th>Close Δ%</th>
            <th>Volume Δ%</th>
          </tr>
        </thead>
        <tbody>`;

  // Limit to top 20 discrepancies for performance with 1-second data
  const limitedDiscrepancies = discrepancies.slice(0, 20);

  limitedDiscrepancies.forEach(discrepancy => {
    const okxData = discrepancy.okx;
    const lambooData = discrepancy.lamboo;
    const priceDiffs = discrepancy.priceDifferences;
    const volDiff = discrepancy.volumeDifference;

    // Helper function to get cell color based on difference
    const getCellColor = (diff) => {
      if (Math.abs(diff) < 1) return '#f8f9fa';
      if (diff > 0) return '#d4edda'; // Green if lamboo > okx
      return '#f8d7da'; // Red if lamboo < okx
    };

    const formatOHLCV = (data) => {
      if (!data) return 'N/A';
      // Preserve raw Unix timestamp in the data array
      return `[${data[0]}, ${parseFloat(data[1]).toFixed(6)}, ${parseFloat(data[2]).toFixed(6)}, ${parseFloat(data[3]).toFixed(6)}, ${parseFloat(data[4]).toFixed(6)}, ${data[5].toLocaleString()}]`;
    };

    html += `
          <tr>
            <td style="white-space: nowrap;">${formatReadableTimestamp(discrepancy.timestamp)}</td>
            <td style="font-family: monospace; font-size: 0.8em; max-width: 300px; word-break: break-all;">${formatOHLCV(okxData)}</td>
            <td style="font-family: monospace; font-size: 0.8em; max-width: 300px; word-break: break-all;">${formatOHLCV(lambooData)}</td>
            <td style="background-color: ${getCellColor(priceDiffs.open)}; text-align: right;">${priceDiffs.open.toFixed(2)}%</td>
            <td style="background-color: ${getCellColor(priceDiffs.high)}; text-align: right;">${priceDiffs.high.toFixed(2)}%</td>
            <td style="background-color: ${getCellColor(priceDiffs.low)}; text-align: right;">${priceDiffs.low.toFixed(2)}%</td>
            <td style="background-color: ${getCellColor(priceDiffs.close)}; text-align: right;">${priceDiffs.close.toFixed(2)}%</td>
            <td style="background-color: ${getCellColor(volDiff)}; text-align: right;">${volDiff.toFixed(2)}%</td>
          </tr>`;
  });

  html += `
        </tbody>
      </table>
    </div>
    <p><small><strong>Chú thích màu sắc:</strong> 🟢 Xanh = Lamboo V2 > OKX | 🔴 Đỏ = Lamboo V2 < OKX | ⚪ Trắng = Chênh lệch < 1%</small></p>`;

  return html;
}

// =============== API FETCH FUNCTIONS ===============
async function fetchOKXData1s(tokenAddress, timeWindow = null) {
  const { startTime, endTime } = timeWindow || calculateRoundedTimeWindow5Minutes();
  const interval = 1;    // 1-second intervals
  const cb = 300;        // 300 candles (5 minutes)
  const to = endTime;
  const from = startTime;

  const config = {
    method: "POST",
    url: "https://api.lamboo.finance/v2/token-detail/ohlcv",
    params: { network: "aptos" },
    data: {
      network: "aptos", address: tokenAddress, timestamp: to, from, to,
      vsToken: "USDC", interval, cb, first: true, isMC: false,
      _: new Date(to * 1000).toISOString()
    },
    headers: {
      accept: "application/json", "content-type": "application/json",
      origin: "https://lamboo.finance", referer: "https://lamboo.finance/",
      clienttimestamp: String(Date.now())
    },
    timeout: 20000  // Standard timeout for 5-minute dataset
  };

  const json = await httpRequest(config.url, config, 'OKX');
  return parseRowsFromAPI(json, 'OKX');
}

async function fetchLambooV2Data1s(tokenAddress, timeWindow = null) {
  const { startTime, endTime } = timeWindow || calculateRoundedTimeWindow5Minutes();
  const interval = 1;    // 1-second intervals
  const cb = 300;        // 300 candles (5 minutes)
  const to = endTime;
  const from = startTime;

  const config = {
    method: "POST",
    url: "https://api.lamboo.finance/v2/token-detail/ohlcv",
    params: { version: 2, network: "aptos" },
    data: {
      network: "aptos", address: tokenAddress, timestamp: to, from, to,
      vsToken: "USDC", interval, cb, first: true, isMC: false,
      _: new Date(to * 1000).toISOString()
    },
    headers: {
      accept: "application/json", "content-type": "application/json",
      origin: "https://lamboo.finance", referer: "https://lamboo.finance/",
      clienttimestamp: String(Date.now())
    },
    timeout: 20000  // Standard timeout for 5-minute dataset
  };

  const json = await httpRequest(config.url, config, 'Lamboo V2');
  return parseRowsFromAPI(json, 'Lamboo V2');
}

// =============== DATA PROCESSING ===============
function alignCandlestickData(okxData, lambooData) {
  const aligned = [];
  const okxMap = new Map(okxData.map(candle => [candle[0], candle]));
  const lambooMap = new Map(lambooData.map(candle => [candle[0], candle]));

  const allTimestamps = new Set([...okxMap.keys(), ...lambooMap.keys()]);

  for (const timestamp of allTimestamps) {
    const okxCandle = okxMap.get(timestamp);
    const lambooCandle = lambooMap.get(timestamp);

    if (okxCandle || lambooCandle) {
      aligned.push({ timestamp, okx: okxCandle || null, lamboo: lambooCandle || null });
    }
  }

  return aligned.sort((a, b) => a.timestamp - b.timestamp);
}

function compareCandlesticks(okxCandle, lambooCandle) {
  if (!okxCandle || !lambooCandle) {
    return { priceDifferences: null, volumeDifference: null, status: 'missing_data' };
  }

  const priceDifferences = {
    open: calculatePercentageDifference(parseFloat(okxCandle[1]), parseFloat(lambooCandle[1])),
    high: calculatePercentageDifference(parseFloat(okxCandle[2]), parseFloat(lambooCandle[2])),
    low: calculatePercentageDifference(parseFloat(okxCandle[3]), parseFloat(lambooCandle[3])),
    close: calculatePercentageDifference(parseFloat(okxCandle[4]), parseFloat(lambooCandle[4]))
  };

  const volumeDifference = calculatePercentageDifference(okxCandle[5], lambooCandle[5]);

  return { priceDifferences, volumeDifference, status: 'compared' };
}

// =============== COMPARISON FUNCTION ===============
async function compareLambooOKX1s(token) {
  console.log(`\n🔍 So sánh 1s ${token.symbol} (${token.name})`);
  console.log(`📍 Address: ${token.address}`);

  // Calculate rounded time window for consistent minute boundaries (5 minutes)
  const timeWindow = calculateRoundedTimeWindow5Minutes();
  const { startTime, endTime } = timeWindow;

  console.log(`⏰ Thời gian phân tích 1s: từ ${formatUTCTimestamp(startTime)} đến ${formatUTCTimestamp(endTime)}`);
  console.log(`📊 Dự kiến: ~300 nến (1 giây/nến, 5 phút)`);

  const errors = { okxErrors: [], lambooErrors: [] };
  let okxData = [], lambooData = [];

  try {
    console.log('📊 Đang lấy dữ liệu 1s từ OKX API...');
    okxData = await fetchOKXData1s(token.address, timeWindow);
    console.log(`✅ OKX: Lấy được ${okxData.length} nến 1s`);
  } catch (error) {
    errors.okxErrors.push(error.message);
    console.error(`❌ OKX Error: ${error.message}`);
  }

  try {
    console.log('📊 Đang lấy dữ liệu 1s từ Lamboo V2 API...');
    lambooData = await fetchLambooV2Data1s(token.address, timeWindow);
    console.log(`✅ Lamboo V2: Lấy được ${lambooData.length} nến 1s`);
  } catch (error) {
    errors.lambooErrors.push(error.message);
    console.error(`❌ Lamboo V2 Error: ${error.message}`);
  }

  if (okxData.length === 0 && lambooData.length === 0) {
    return {
      token,
      summary: { totalCandles: 0, matchingCandles: 0, averagePriceDifference: null, averageVolumeDifference: null },
      differences: [], errors, timeRange: timeWindow, significantDiscrepancies: []
    };
  }

  console.log('🔄 Đang xử lý và so sánh dữ liệu 1s...');
  const alignedData = alignCandlestickData(okxData, lambooData);
  const differences = [];
  let totalPriceDifferences = { open: 0, high: 0, low: 0, close: 0 };
  let totalVolumeDifference = 0, matchingCandles = 0;
  const significantDiscrepancies = [];

  // Process data in chunks for better memory management with large datasets
  const chunkSize = 1000;
  for (let i = 0; i < alignedData.length; i += chunkSize) {
    const chunk = alignedData.slice(i, i + chunkSize);

    for (const aligned of chunk) {
      const comparison = compareCandlesticks(aligned.okx, aligned.lamboo);
      const difference = {
        timestamp: new Date(aligned.timestamp * 1000).toISOString(),
        okx: aligned.okx, lamboo: aligned.lamboo, ...comparison
      };

      differences.push(difference);

      if (comparison.status === 'compared') {
        matchingCandles++;
        totalPriceDifferences.open += Math.abs(comparison.priceDifferences.open);
        totalPriceDifferences.high += Math.abs(comparison.priceDifferences.high);
        totalPriceDifferences.low += Math.abs(comparison.priceDifferences.low);
        totalPriceDifferences.close += Math.abs(comparison.priceDifferences.close);
        totalVolumeDifference += Math.abs(comparison.volumeDifference);

        // Check for significant discrepancies (>5% in any OHLC field)
        const maxPriceDiff = Math.max(
          Math.abs(comparison.priceDifferences.open),
          Math.abs(comparison.priceDifferences.high),
          Math.abs(comparison.priceDifferences.low),
          Math.abs(comparison.priceDifferences.close)
        );

        if (maxPriceDiff > 5) {
          significantDiscrepancies.push({
            ...difference,
            maxPriceDiff
          });
        }
      }
    }

    // Progress indicator for large datasets
    if (i % 1000 === 0 && i > 0) {
      console.log(`  📈 Đã xử lý ${i}/${alignedData.length} nến...`);
    }
  }

  // Sort and limit significant discrepancies for performance
  const topDiscrepancies = significantDiscrepancies
    .sort((a, b) => b.maxPriceDiff - a.maxPriceDiff)
    .slice(0, 50); // Limit to top 50 for 1-second data

  const averagePriceDifference = matchingCandles > 0 ?
    (totalPriceDifferences.open + totalPriceDifferences.high + totalPriceDifferences.low + totalPriceDifferences.close) / (4 * matchingCandles) : null;

  const averageVolumeDifference = matchingCandles > 0 ? totalVolumeDifference / matchingCandles : null;

  console.log(`📈 Kết quả so sánh 1s:`);
  console.log(`  - Tổng số nến: ${alignedData.length}`);
  console.log(`  - Nến khớp: ${matchingCandles}`);
  console.log(`  - Chênh lệch giá trung bình: ${averagePriceDifference?.toFixed(4)}%`);
  console.log(`  - Chênh lệch volume trung bình: ${averageVolumeDifference?.toFixed(2)}%`);
  console.log(`  - Chênh lệch đáng kể (>5%): ${topDiscrepancies.length}`);

  return {
    token,
    summary: { totalCandles: alignedData.length, matchingCandles, averagePriceDifference, averageVolumeDifference },
    differences, errors, timeRange: timeWindow, significantDiscrepancies: topDiscrepancies
  };
}

// =============== HTML REPORT GENERATION ===============
function generateHTMLReport1s(results) {
  const timestamp = new Date().toISOString();
  const totalTokens = results.length;
  const successfulComparisons = results.filter(r => r.summary.totalCandles > 0);

  // Calculate time range from first successful result
  let timeRangeText = 'N/A';
  if (successfulComparisons.length > 0 && successfulComparisons[0].timeRange) {
    const { startTime, endTime } = successfulComparisons[0].timeRange;
    timeRangeText = `từ ${formatUTCTimestamp(startTime)} đến ${formatUTCTimestamp(endTime)}`;
  }

  let html = `<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Báo cáo So sánh OHLCV 1-Second - Lamboo V2 vs OKX</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; }
        .header h1 { margin: 0; font-size: 2.2em; }
        .header p { margin: 10px 0 0 0; font-size: 1.1em; opacity: 0.9; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .summary-card h3 { margin: 0 0 10px 0; color: #495057; font-size: 0.9em; text-transform: uppercase; }
        .summary-card .value { font-size: 2em; font-weight: bold; color: #007bff; margin: 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 0.95em; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: 600; color: #495057; position: sticky; top: 0; }
        tr:hover { background-color: #f8f9fa; }
        .token-info { max-width: 300px; }
        .token-name { font-weight: bold; color: #495057; display: block; }
        .token-symbol { color: #6c757d; font-size: 0.9em; display: block; }
        .token-address { font-family: monospace; font-size: 0.8em; color: #6c757d; word-break: break-all; margin-top: 5px; }
        .number { text-align: right; font-family: monospace; }
        .percentage { font-weight: bold; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; color: white; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .section h3 { margin-top: 0; color: #495057; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        .footer { text-align: center; margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px; color: #6c757d; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .warning h4 { margin: 0 0 10px 0; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Báo cáo So sánh OHLCV 1-Second</h1>
            <p>Lamboo V2 API vs OKX API - Timeframe: 1 giây</p>
            <p>Tạo lúc: ${timestamp}</p>
            <p>Khoảng thời gian phân tích: ${timeRangeText}</p>
        </div>

        <div class="warning">
            <h4>⚠️ Lưu ý về dữ liệu 1-second:</h4>
            <p>• Timeframe 1 giây với window 5 phút tạo ra ~300 nến</p>
            <p>• Chỉ hiển thị top 20 chênh lệch đáng kể để tối ưu hiệu suất</p>
            <p>• Phù hợp cho phân tích chi tiết trong thời gian ngắn</p>
            <p>• Dữ liệu có độ chính xác cao, phù hợp cho high-frequency analysis</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>Tổng số Tokens</h3>
                <p class="value">${totalTokens}</p>
            </div>
            <div class="summary-card">
                <h3>So sánh thành công</h3>
                <p class="value">${successfulComparisons.length}</p>
            </div>
            <div class="summary-card">
                <h3>Timeframe</h3>
                <p class="value">1s</p>
            </div>
            <div class="summary-card">
                <h3>Thời gian phân tích</h3>
                <p class="value">5 phút</p>
            </div>
        </div>

        <div class="section">
            <h3>📋 Kết quả So sánh Chi tiết</h3>
            <div style="overflow-x: auto;">
                <table>
                    <thead>
                        <tr>
                            <th>Token Info</th>
                            <th>Tổng nến</th>
                            <th>Nến khớp</th>
                            <th>Tỷ lệ khớp</th>
                            <th>Chênh lệch giá TB</th>
                            <th>Chênh lệch volume TB</th>
                            <th>Trạng thái</th>
                            <th>Errors</th>
                            <th>Chênh lệch >5%</th>
                        </tr>
                    </thead>
                    <tbody>`;

  results.forEach(result => {
    const token = result.token;
    const summary = result.summary;
    const errors = result.errors;
    const discrepancies = result.significantDiscrepancies || [];

    const matchRate = summary.totalCandles > 0 ?
      (summary.matchingCandles / summary.totalCandles * 100).toFixed(1) : '0.0';

    const statusColor = getStatusColor(summary.averagePriceDifference, summary.averageVolumeDifference);
    const statusText = getStatusText(summary.averagePriceDifference, summary.averageVolumeDifference);

    const errorCount = (errors.okxErrors?.length || 0) + (errors.lambooErrors?.length || 0);
    const hasErrors = errorCount > 0;

    html += `
                <tr style="background-color: ${statusColor};">
                    <td class="token-info">
                        <span class="token-name">${token.name}</span>
                        <span class="token-symbol">${token.symbol}</span>
                        <div class="token-address">${token.address}</div>
                    </td>
                    <td class="number">${formatNumber(summary.totalCandles)}</td>
                    <td class="number">${formatNumber(summary.matchingCandles)}</td>
                    <td class="number">${matchRate}%</td>
                    <td class="number percentage">${formatPercentage(summary.averagePriceDifference)}</td>
                    <td class="number percentage">${formatPercentage(summary.averageVolumeDifference)}</td>
                    <td><span class="status" style="background-color: ${statusColor};">${statusText}</span></td>
                    <td class="number ${hasErrors ? 'negative' : 'positive'}">${errorCount}</td>
                    <td class="number ${discrepancies.length > 0 ? 'negative' : 'positive'}">${discrepancies.length}</td>
                </tr>`;
  });

  html += `
                    </tbody>
                </table>
            </div>
        </div>`;

  // Add detailed discrepancy analysis for each token
  results.forEach(result => {
    if (result.significantDiscrepancies && result.significantDiscrepancies.length > 0) {
      html += `
        <div class="section">
            <h3>🔍 Chi tiết chênh lệch - ${result.token.symbol} (${result.token.name})</h3>
            ${generateDiscrepancyTable(result.significantDiscrepancies)}
        </div>`;
    }
  });

  html += `
        <div class="footer">
            <p><strong>Lamboo V2 vs OKX OHLCV Comparison Report - 1-Second Timeframe</strong></p>
            <p>Generated at: ${timestamp}</p>
            <p>Timeframe: 1 second intervals | Analysis Period: 5 minutes | Expected Candles: ~300</p>
            <p><em>Chú thích: Chênh lệch >5% được đánh dấu và hiển thị chi tiết. Dữ liệu 1-second có volume cao, chỉ hiển thị các chênh lệch đáng kể nhất.</em></p>
        </div>
    </div>
</body>
</html>`;

  return html;
}

// =============== MAIN EXECUTION ===============
async function main() {
  console.log('🚀 Bắt đầu so sánh nâng cao Lamboo V2 vs OKX - 1-SECOND TIMEFRAME');
  console.log('📊 Phân tích tất cả tokens trong tokenList với dữ liệu 1 giây (5 phút)...');
  console.log('⚠️  Lưu ý: Dữ liệu 1s với window 5 phút (~300 nến), phù hợp cho high-frequency analysis');

  if (DEBUG_MODE) {
    console.log('🐛 DEBUG MODE ENABLED - API requests will be logged with cURL commands');
  }

  console.log('=' .repeat(80));

  const results = [];
  const activeTokens = tokenData.filter(token => token && token.address && token.symbol);

  if (activeTokens.length === 0) {
    console.error('❌ Không tìm thấy token nào trong tokenList');
    return;
  }

  console.log(`📋 Tìm thấy ${activeTokens.length} token(s) để phân tích với timeframe 1s`);

  for (let i = 0; i < activeTokens.length; i++) {
    const token = activeTokens[i];

    console.log(`\n⏳ Đang xử lý ${i + 1}/${activeTokens.length}: ${token.symbol} (1-second data)`);

    try {
      const result = await compareLambooOKX1s(token);
      results.push(result);

      // Standard delay for 1-second data (5-minute window)
      if (i < activeTokens.length - 1) {
        console.log('⏱️  Chờ 2 giây trước khi xử lý token tiếp theo...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

    } catch (error) {
      console.error(`❌ Lỗi khi xử lý ${token.symbol}: ${error.message}`);
      results.push({
        token,
        summary: {
          totalCandles: 0,
          matchingCandles: 0,
          averagePriceDifference: null,
          averageVolumeDifference: null
        },
        differences: [],
        errors: {
          okxErrors: [error.message],
          lambooErrors: []
        },
        timeRange: null,
        significantDiscrepancies: []
      });
    }
  }

  // Generate reports
  console.log('\n📊 Tạo báo cáo 1-second...');

  // Save JSON report
  const jsonPath = path.join(__dirname, '..', 'outputs', 'json', `lamboo_okx_1s.json`);
  const jsonReport = {
    generatedAt: new Date().toISOString(),
    timeframe: '1s',
    period: '5 minutes',
    totalTokens: results.length,
    results
  };

  // Ensure output directory exists
  const jsonDir = path.dirname(jsonPath);
  if (!fs.existsSync(jsonDir)) {
    fs.mkdirSync(jsonDir, { recursive: true });
  }

  fs.writeFileSync(jsonPath, JSON.stringify(jsonReport, null, 2));
  console.log(`💾 Đã lưu báo cáo JSON: ${jsonPath}`);

  // Generate and save HTML report
  const htmlPath = path.join(__dirname, '..', 'outputs', 'html', `lamboo_okx_1s.html`);
  const htmlReport = generateHTMLReport1s(results);

  // Ensure output directory exists
  const htmlDir = path.dirname(htmlPath);
  if (!fs.existsSync(htmlDir)) {
    fs.mkdirSync(htmlDir, { recursive: true });
  }

  fs.writeFileSync(htmlPath, htmlReport);
  console.log(`📊 Đã tạo báo cáo HTML: ${htmlPath}`);

  // Summary statistics
  console.log('\n' + '=' .repeat(80));
  console.log('📈 TỔNG KẾT 1-SECOND ANALYSIS:');
  console.log(`  📊 Tổng số tokens: ${results.length}`);

  const successfulComparisons = results.filter(r => r.summary.totalCandles > 0);
  console.log(`  ✅ So sánh thành công: ${successfulComparisons.length}`);
  console.log(`  ❌ Thất bại: ${results.length - successfulComparisons.length}`);

  if (successfulComparisons.length > 0) {
    const totalCandles = successfulComparisons.reduce((sum, r) => sum + r.summary.totalCandles, 0);
    const totalMatching = successfulComparisons.reduce((sum, r) => sum + r.summary.matchingCandles, 0);
    const matchRate = totalCandles > 0 ? (totalMatching / totalCandles * 100).toFixed(2) : '0.00';

    console.log(`  🕯️  Tổng số nến 1s: ${totalCandles.toLocaleString()}`);
    console.log(`  🎯 Nến khớp: ${totalMatching.toLocaleString()}`);
    console.log(`  📊 Tỷ lệ khớp: ${matchRate}%`);

    const avgPriceDiffs = successfulComparisons
      .map(r => r.summary.averagePriceDifference)
      .filter(d => d !== null);

    const avgVolDiffs = successfulComparisons
      .map(r => r.summary.averageVolumeDifference)
      .filter(d => d !== null);

    if (avgPriceDiffs.length > 0) {
      const overallAvgPriceDiff = avgPriceDiffs.reduce((sum, d) => sum + Math.abs(d), 0) / avgPriceDiffs.length;
      console.log(`  💰 Chênh lệch giá trung bình: ${overallAvgPriceDiff.toFixed(4)}%`);
    }

    if (avgVolDiffs.length > 0) {
      const overallAvgVolDiff = avgVolDiffs.reduce((sum, d) => sum + Math.abs(d), 0) / avgVolDiffs.length;
      console.log(`  📊 Chênh lệch volume trung bình: ${overallAvgVolDiff.toFixed(2)}%`);
    }

    const totalDiscrepancies = successfulComparisons.reduce((sum, r) => sum + (r.significantDiscrepancies?.length || 0), 0);
    console.log(`  ⚠️  Tổng chênh lệch đáng kể (>5%): ${totalDiscrepancies}`);
  }

  console.log('\n✅ Hoàn thành tất cả so sánh 1-second!');
  console.log('📝 Lưu ý: Dữ liệu 1-second cung cấp độ chi tiết cao nhưng cần thời gian xử lý lâu hơn');
}

// Check if running directly
if (require.main === module) {
  // Set environment
  const environment = process.env.NODE_ENV || 'production';
  const baseURL = environment === 'development' ? 'http://localhost:3001' : 'https://api.lamboo.finance';
  console.log(`🌍 Using ${environment} environment: ${baseURL}`);

  main().catch(console.error);
}

module.exports = {
  compareLambooOKX1s,
  generateHTMLReport1s,
  calculateRoundedTimeWindow5Minutes,
  fetchOKXData1s,
  fetchLambooV2Data1s
};
