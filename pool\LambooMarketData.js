/**
 * Extract data from Lamb<PERSON> pool_list API for multiple tokens from tokenList.js
 * Simple one-command script: just run `node LambooMarketData.js`
 * 
 * Gets token addresses from tokenList.js and fetches data for all tokens.
 * Fields: dex, pair_address, liquidity, buys, sells, volume_buy, volume_sell,
 * buyers, sellers, txns, volume, makers
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Import token list and API configuration
const { tokenData, API_BASE_URL, CURRENT_ENV } = require('../tokenList.js');

// === CONFIG ===
const CONFIG = {
  NETWORK: 'aptos',
  LIMIT_PER_TOKEN: 30, // Maximum pools per token
  MAX_TOKENS: 30, // Maximum tokens to process (set to -1 for all tokens)
  OUT_FILE: '../results/LambooMarketData.json',
  DELAY_BETWEEN_REQUESTS: 500, // ms delay between API calls
};

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

async function fetchTokenPools(tokenAddress, tokenInfo) {
  const url = `${API_BASE_URL}/tokenlist/pools?network=aptos`;
  const payload = {
    token: tokenAddress,
    sort_by: 'volume',
    order: 'desc',
    limit: CONFIG.LIMIT_PER_TOKEN,
    page: 1,
  };

  const headers = {
    'accept': 'application/json',
    'content-type': 'application/json',
    'origin': 'https://lamboo.finance',
    'referer': 'https://lamboo.finance/',
    'clienttimestamp': String(Date.now()),
    'user-agent': 'Mozilla/5.0',
  };

  try {
    const res = await axios.post(url, payload, { headers, timeout: 30000 });
    const pools = res.data?.data?.pool_list || [];

    return pools.map((p) => ({
      dex: p.dex,
      pair_address: p.pair_address,
      liquidity: p.liquidity,
      buys: p.data?.buys,
      sells: p.data?.sells,
      volume_buy: p.data?.volume_buy,
      volume_sell: p.data?.volume_sell,
      buyers: p.data?.buyers,
      sellers: p.data?.sellers,
      txns: p.data?.txns,
      volume: p.data?.volume,
      makers: p.data?.makers,
      // Add source token info
      sourceToken: {
        address: tokenInfo.address,
        symbol: tokenInfo.symbol,
        name: tokenInfo.name
      }
    }));
  } catch (err) {
    console.error(`  Lỗi khi lấy dữ liệu cho token ${tokenInfo.symbol}:`, err.message);
    return [];
  }
}

async function main() {
  console.log(`Đang xử lý ${tokenData.length} token từ tokenList.js...`);
  
  const tokensToProcess = CONFIG.MAX_TOKENS === -1 ? tokenData : tokenData.slice(0, CONFIG.MAX_TOKENS);
  console.log(`Sẽ xử lý ${tokensToProcess.length} token đầu tiên.`);

  const allResults = [];
  
  for (let tokenIndex = 0; tokenIndex < tokensToProcess.length; tokenIndex++) {
    const token = tokensToProcess[tokenIndex];
    console.log(`\n[${tokenIndex + 1}/${tokensToProcess.length}] Xử lý token: ${token.symbol} (${token.name})`);
    console.log(`Token address: ${token.address}`);
    
    try {
      const pools = await fetchTokenPools(token.address, token);

      // Filter out pools with volume = 0
      const validPools = pools.filter(pool => {
        const volume = pool.volume || 0;
        return volume > 0;
      });

      if (pools.length === 0) {
        console.log(`  Không tìm thấy pool nào cho token ${token.symbol}`);
      } else {
        const filteredCount = pools.length - validPools.length;
        console.log(`  Tìm thấy ${pools.length} pool(s) cho token ${token.symbol}`);
        if (filteredCount > 0) {
          console.log(`  Đã loại bỏ ${filteredCount} pool(s) có volume = 0`);
        }
        console.log(`  Lưu ${validPools.length} pool(s) hợp lệ`);
        allResults.push(...validPools);
      }
      
    } catch (error) {
      console.error(`  Lỗi khi xử lý token ${token.symbol}:`, error.message);
    }
    
    // Delay between tokens to avoid rate limiting
    if (tokenIndex < tokensToProcess.length - 1) {
      await sleep(CONFIG.DELAY_BETWEEN_REQUESTS);
    }
  }

  // Save results
  const outputPath = path.resolve(__dirname, CONFIG.OUT_FILE);
  fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2));
  console.log(`\nĐã lưu ${allResults.length} record vào ${outputPath}`);
  
  // Summary by token
  const summary = {};
  allResults.forEach(record => {
    const symbol = record.sourceToken.symbol;
    if (!summary[symbol]) summary[symbol] = 0;
    summary[symbol]++;
  });
  
  console.log('\nTóm tắt kết quả:');
  Object.entries(summary).forEach(([symbol, count]) => {
    console.log(`  ${symbol}: ${count} pool(s)`);
  });

  // Summary by DEX
  const dexSummary = {};
  allResults.forEach(record => {
    const dex = record.dex;
    if (!dexSummary[dex]) dexSummary[dex] = 0;
    dexSummary[dex]++;
  });
  
  console.log('\nTóm tắt theo DEX:');
  Object.entries(dexSummary).forEach(([dex, count]) => {
    console.log(`  ${dex}: ${count} pool(s)`);
  });
}

main();
