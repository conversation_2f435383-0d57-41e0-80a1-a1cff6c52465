#!/usr/bin/env node

/**
 * Cellana Finance API Data Fetcher
 * 
 * <PERSON><PERSON>t để fetch pool data từ Cellana Finance API
 * và lưu response vào file JSON với error handling và logging đầy đủ.
 */

const https = require('https');
const fs = require('fs').promises;
const path = require('path');

class Logger {
    constructor() {
        this.logFile = 'cellana_finance_fetcher.log';
    }

    async log(level, message) {
        const timestamp = new Date().toISOString();
        const logMessage = `${timestamp} - ${level} - ${message}\n`;
        
        // Log to console
        console.log(`${timestamp} - ${level} - ${message}`);
        
        // Log to file
        try {
            await fs.appendFile(this.logFile, logMessage);
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    async info(message) {
        await this.log('INFO', message);
    }

    async error(message) {
        await this.log('ERROR', message);
    }

    async warning(message) {
        await this.log('WARNING', message);
    }
}

class CellanaFinanceAPI {
    constructor() {
        this.baseUrl = 'https://api-v2.cellana.finance/api/v1/pool/0x0';
        this.queryParams = {
            address: '0x0',
            page: '1',
            limit: '10',
            poolType: 'ALL_POOLS',
            search: '0x6c2b2c3d5140ee973622ea3fa75a86d0706551864a33aa0feac06bdfc788841b',
            votingRewards: '',
            isVotePage: '0',
            '': 'null'
        };
        this.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6',
            'origin': 'https://app.cellana.finance',
            'priority': 'u=1, i',
            'referer': 'https://app.cellana.finance/',
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36'
        };
        this.logger = new Logger();
    }

    /**
     * Tạo query string từ parameters
     * @returns {string} Query string
     */
    buildQueryString() {
        const params = new URLSearchParams();
        Object.entries(this.queryParams).forEach(([key, value]) => {
            params.append(key, value);
        });
        return params.toString();
    }

    /**
     * Fetch pool data từ Cellana Finance API
     * @param {number} timeout - Request timeout in milliseconds
     * @returns {Promise<Object>} API response data
     */
    async fetchPoolData(timeout = 30000) {
        return new Promise((resolve, reject) => {
            const queryString = this.buildQueryString();
            const url = `${this.baseUrl}?${queryString}`;
            
            this.logger.info(`Đang fetch data từ: ${url}`);

            const options = {
                method: 'GET',
                headers: this.headers,
                timeout: timeout
            };

            const req = https.request(url, options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        if (res.statusCode !== 200) {
                            throw new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`);
                        }

                        const jsonData = JSON.parse(data);
                        this.logger.info('Fetch pool data thành công');

                        // Log basic info về response
                        if (jsonData.data && Array.isArray(jsonData.data)) {
                            this.logger.info(`Số lượng pools: ${jsonData.data.length}`);

                            // Log tổng TVL từ response
                            if (jsonData.totalTvl) {
                                this.logger.info(`Tổng TVL (từ API): $${jsonData.totalTvl.toLocaleString()}`);
                            }

                            // Log TVL của pools được trả về
                            let poolsTVL = 0;
                            jsonData.data.forEach(item => {
                                const pool = item.pool || item;
                                if (pool.tvl) poolsTVL += parseFloat(pool.tvl) || 0;
                            });

                            if (poolsTVL > 0) this.logger.info(`TVL pools hiện tại: $${poolsTVL.toLocaleString()}`);
                        }

                        resolve(jsonData);
                    } catch (error) {
                        this.logger.error(`Lỗi parse JSON response: ${error.message}`);
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                this.logger.error(`Request error: ${error.message}`);
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                const error = new Error(`Request timeout sau ${timeout}ms`);
                this.logger.error(error.message);
                reject(error);
            });

            req.setTimeout(timeout);
            req.end();
        });
    }

    /**
     * Lưu data vào file JSON
     * @param {Object} data - Data cần lưu
     * @param {string} filename - Tên file (optional)
     * @returns {Promise<boolean>} Success status
     */
    async saveToFile(data, filename = null) {
        try {
            if (!filename) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                filename = `cellana_finance_data_${timestamp}.json`;
            }

            // Tạo outputs directory nếu chưa có
            const outputDir = 'outputs';
            try {
                await fs.access(outputDir);
            } catch {
                await fs.mkdir(outputDir, { recursive: true });
                this.logger.info(`Đã tạo thư mục: ${outputDir}`);
            }

            const filepath = path.join(outputDir, filename);
            await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf8');

            this.logger.info(`Data đã được lưu vào: ${filepath}`);
            return true;
        } catch (error) {
            this.logger.error(`Lỗi khi lưu file: ${error.message}`);
            return false;
        }
    }

    /**
     * Trích xuất key metrics từ API response
     * @param {Object} data - Full API response
     * @returns {Object} Key metrics
     */
    extractKeyMetrics(data) {
        try {
            const poolItems = data.data || [];

            let totalTVL = 0;
            const poolSummaries = [];

            poolItems.forEach(item => {
                const pool = item.pool || item;
                const tvl = parseFloat(pool.tvl) || 0;
                const apr = parseFloat(pool.apr) || 0;

                totalTVL += tvl;

                // Extract token information
                const token0 = pool.token0 || {};
                const token1 = pool.token1 || {};
                const gauge = pool.gauge || {};

                poolSummaries.push({
                    pool_address: pool.address,
                    pool_name: pool.name || pool.symbol,
                    pool_symbol: pool.symbol,
                    tvl: tvl,
                    apr: apr,
                    stable: pool.stable,
                    total_supply: parseFloat(pool.totalSupply) || 0,
                    reserve0: parseFloat(pool.reserve0) || 0,
                    reserve1: parseFloat(pool.reserve1) || 0,
                    token0: {
                        symbol: token0.symbol,
                        name: token0.name,
                        address: token0.address,
                        decimals: token0.decimals
                    },
                    token1: {
                        symbol: token1.symbol,
                        name: token1.name,
                        address: token1.address,
                        decimals: token1.decimals
                    },
                    gauge: {
                        address: gauge.address,
                        status: gauge.gaugeStatus,
                        apr: parseFloat(gauge.apr) || 0,
                        votes: gauge.votes || 0,
                        tbv: parseFloat(gauge.tbv) || 0
                    },
                    staked0: parseFloat(item.staked0) || 0,
                    staked1: parseFloat(item.staked1) || 0
                });
            });

            const metrics = {
                timestamp: new Date().toISOString(),
                total_pools: poolItems.length,
                total_tvl_from_api: parseFloat(data.totalTvl) || 0,
                pools_tvl_sum: totalTVL,
                total_pages: data.totalPages || 1,
                pools: poolSummaries,
                query_params: this.queryParams
            };

            return metrics;
        } catch (error) {
            this.logger.error(`Lỗi khi trích xuất key metrics: ${error.message}`);
            return {};
        }
    }

    /**
     * Log chi tiết về pools data
     * @param {Object} data - API response data
     */
    async logPoolsDetails(data) {
        try {
            if (!data.data || !Array.isArray(data.data)) return;

            const poolItems = data.data;

            await this.logger.info('=== CELLANA FINANCE POOLS DETAILS ===');
            await this.logger.info(`Tổng số pools: ${poolItems.length}`);
            await this.logger.info(`Tổng TVL (từ API): $${parseFloat(data.totalTvl)?.toLocaleString() || 'N/A'}`);
            await this.logger.info(`Tổng pages: ${data.totalPages || 1}`);

            if (poolItems.length > 0) {
                await this.logger.info('\nChi tiết từng pool:');

                for (let i = 0; i < poolItems.length; i++) {
                    const item = poolItems[i];
                    const pool = item.pool || item;
                    const token0 = pool.token0 || {};
                    const token1 = pool.token1 || {};
                    const gauge = pool.gauge || {};

                    await this.logger.info(`\n--- Pool ${i + 1} ---`);
                    await this.logger.info(`Name: ${pool.name || pool.symbol || 'N/A'}`);
                    await this.logger.info(`Symbol: ${pool.symbol || 'N/A'}`);
                    await this.logger.info(`Address: ${pool.address || 'N/A'}`);
                    await this.logger.info(`Stable: ${pool.stable ? 'Yes' : 'No'}`);
                    await this.logger.info(`TVL: $${parseFloat(pool.tvl)?.toLocaleString() || 'N/A'}`);
                    await this.logger.info(`APR: ${(parseFloat(pool.apr) * 100)?.toFixed(2) || 'N/A'}%`);
                    await this.logger.info(`Total Supply: ${parseFloat(pool.totalSupply)?.toLocaleString() || 'N/A'}`);

                    // Token information
                    await this.logger.info(`Token 0: ${token0.symbol || 'N/A'} (${token0.name || 'N/A'})`);
                    await this.logger.info(`Token 1: ${token1.symbol || 'N/A'} (${token1.name || 'N/A'})`);
                    await this.logger.info(`Reserve 0: ${parseFloat(pool.reserve0)?.toLocaleString() || 'N/A'}`);
                    await this.logger.info(`Reserve 1: ${parseFloat(pool.reserve1)?.toLocaleString() || 'N/A'}`);

                    // Gauge information
                    if (gauge.address) {
                        await this.logger.info(`Gauge Status: ${gauge.gaugeStatus || 'N/A'}`);
                        await this.logger.info(`Gauge APR: ${(parseFloat(gauge.apr) * 100)?.toFixed(2) || 'N/A'}%`);
                        await this.logger.info(`Votes: ${gauge.votes || 0}`);
                        await this.logger.info(`TBV: $${parseFloat(gauge.tbv)?.toFixed(6) || 'N/A'}`);
                    }

                    // Staking information
                    await this.logger.info(`Staked 0: ${parseFloat(item.staked0) || 0}`);
                    await this.logger.info(`Staked 1: ${parseFloat(item.staked1) || 0}`);
                }
            }

            await this.logger.info('\n=========================================');
        } catch (error) {
            await this.logger.error(`Lỗi khi log pools details: ${error.message}`);
        }
    }
}

/**
 * Main function
 */
async function main() {
    const api = new CellanaFinanceAPI();

    try {
        await api.logger.info('🚀 Bắt đầu fetch Cellana Finance data');

        // Fetch data từ API
        const data = await api.fetchPoolData();

        // Log chi tiết về pools
        await api.logPoolsDetails(data);

        // Lưu complete response
        const success = await api.saveToFile(data);

        if (success) {
            // Trích xuất và lưu key metrics
            const metrics = api.extractKeyMetrics(data);
            if (Object.keys(metrics).length > 0) {
                await api.logger.info('✅ Key metrics đã được trích xuất thành công');

                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                const metricsFilename = `cellana_finance_metrics_${timestamp}.json`;
                await api.saveToFile(metrics, metricsFilename);
            }
        }

        await api.logger.info('🎉 Hoàn thành fetch Cellana Finance data');
        process.exit(0);

    } catch (error) {
        await api.logger.error(`❌ Lỗi khi fetch data: ${error.message}`);
        process.exit(1);
    }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
    const logger = new Logger();
    await logger.error(`Unhandled Rejection: ${reason}`);
    process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
    const logger = new Logger();
    await logger.error(`Uncaught Exception: ${error.message}`);
    process.exit(1);
});

// Chạy main function nếu script được execute trực tiếp
if (require.main === module) {
    main();
}

module.exports = { CellanaFinanceAPI };
