/**
 * Fetch token metrics from OKX API for tokens in tokenList.js
 * Simple one-command script: just run `node OKXMetricData.js`
 * 
 * Gets token addresses from tokenList.js and fetches trading data.
 * API: https://web3.okx.com/priapi/v1/dx/market/v2/trading-history/info
 * Timeframes: 5m (type=4), 1h (type=1), 24h (type=3)
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Import token list
const { tokenData } = require('../tokenList.js');

// === CONFIG ===
const CONFIG = {
  CHAIN_ID: 637, // Aptos chain ID
  MAX_TOKENS: 30, // Maximum tokens to process (set to -1 for all tokens)
  OUT_FILE: '../results/OKXMetricData.json',
  DELAY_BETWEEN_REQUESTS: 800, // ms delay between API calls
  DELAY_BETWEEN_TIMEFRAMES: 300, // ms delay between timeframe calls for same token
  TIMEOUT: 30000, // Request timeout in ms
  TIMEFRAMES: [
    { type: 4, name: '5m' },
    { type: 1, name: '1h' },
    { type: 3, name: '24h' }
  ]
};

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

async function fetchOKXMetrics(tokenAddress, timeframeType, timeframeName) {
  const timestamp = Date.now();
  const url = `https://web3.okx.com/priapi/v1/dx/market/v2/trading-history/info?type=${timeframeType}&chainId=${CONFIG.CHAIN_ID}&tokenContractAddress=${encodeURIComponent(tokenAddress)}&t=${timestamp}`;
  
  const headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US,en;q=0.9',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'referer': 'https://www.okx.com/',
    'origin': 'https://www.okx.com'
  };

  try {
    const res = await axios.get(url, { headers, timeout: CONFIG.TIMEOUT });
    
    if (res.data.code !== 0 || !res.data.data) {
      console.log(`    ❌ ${timeframeName}: ${res.data.msg || 'No data'}`);
      return null;
    }

    const data = res.data.data;
    
    // Extract and format the data
    const result = {
      buyVol: parseFloat(data.buyAmountUsd) || 0,
      buyTxns: parseInt(data.buyNo) || 0,
      buyTraders: parseInt(data.buyTraders) || 0,
      sellVol: parseFloat(data.sellAmountUsd) || 0,
      sellTxns: parseInt(data.sellNo) || 0,
      sellTraders: parseInt(data.sellTraders) || 0,
      totalVol: parseFloat(data.totalAmountUsd) || 0,
      totalTxns: parseInt(data.totalNo) || 0,
      totalTraders: parseInt(data.totalTraders) || 0,
      netVol: parseFloat(data.inflow) || 0, // inflow = net volume
    };

    console.log(`    ✅ ${timeframeName}: Vol=$${result.totalVol.toLocaleString()}, Txns=${result.totalTxns.toLocaleString()}`);
    return result;
    
  } catch (err) {
    const status = err?.response?.status;
    const message = err?.response?.data?.msg || err.message;
    console.log(`    ❌ ${timeframeName}: Error (${status}): ${message}`);
    return null;
  }
}

async function fetchTokenAllTimeframes(tokenAddress, tokenInfo) {
  console.log(`  Đang lấy dữ liệu cho ${CONFIG.TIMEFRAMES.length} timeframes...`);
  
  const result = {
    address: tokenAddress,
    symbol: tokenInfo.symbol,
    name: tokenInfo.name,
    data: {}
  };

  for (let i = 0; i < CONFIG.TIMEFRAMES.length; i++) {
    const timeframe = CONFIG.TIMEFRAMES[i];
    
    const metrics = await fetchOKXMetrics(tokenAddress, timeframe.type, timeframe.name);
    
    if (metrics) {
      result.data[timeframe.name] = metrics;
    } else {
      result.data[timeframe.name] = {
        buyVol: 0, buyTxns: 0, buyTraders: 0,
        sellVol: 0, sellTxns: 0, sellTraders: 0,
        totalVol: 0, totalTxns: 0, totalTraders: 0,
        netVol: 0
      };
    }
    
    // Delay between timeframes for same token
    if (i < CONFIG.TIMEFRAMES.length - 1) {
      await sleep(CONFIG.DELAY_BETWEEN_TIMEFRAMES);
    }
  }

  return result;
}

async function main() {
  console.log(`Đang xử lý ${tokenData.length} token từ tokenList.js...`);
  
  const tokensToProcess = CONFIG.MAX_TOKENS === -1 ? tokenData : tokenData.slice(0, CONFIG.MAX_TOKENS);
  console.log(`Sẽ xử lý ${tokensToProcess.length} token đầu tiên.`);

  const allResults = [];
  
  for (let tokenIndex = 0; tokenIndex < tokensToProcess.length; tokenIndex++) {
    const token = tokensToProcess[tokenIndex];
    console.log(`\n[${tokenIndex + 1}/${tokensToProcess.length}] Xử lý token: ${token.symbol} (${token.name})`);
    console.log(`Token address: ${token.address}`);
    
    try {
      const tokenMetrics = await fetchTokenAllTimeframes(token.address, token);
      allResults.push(tokenMetrics);
      
      // Summary for this token
      const vol24h = tokenMetrics.data['24h']?.totalVol || 0;
      const txns24h = tokenMetrics.data['24h']?.totalTxns || 0;
      const netVol24h = tokenMetrics.data['24h']?.netVol || 0;
      console.log(`  📊 Summary 24h: Vol=$${vol24h.toLocaleString()}, Txns=${txns24h.toLocaleString()}, NetVol=$${netVol24h.toLocaleString()}`);
      
    } catch (error) {
      console.error(`  ❌ Lỗi khi xử lý token ${token.symbol}:`, error.message);
    }
    
    // Delay between tokens to avoid rate limiting
    if (tokenIndex < tokensToProcess.length - 1) {
      console.log(`  Đợi ${CONFIG.DELAY_BETWEEN_REQUESTS}ms...`);
      await sleep(CONFIG.DELAY_BETWEEN_REQUESTS);
    }
  }

  // Save results
  const outputPath = path.resolve(__dirname, CONFIG.OUT_FILE);
  fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2));
  console.log(`\n✅ Đã lưu ${allResults.length} record vào ${outputPath}`);
  
  // Summary by token
  console.log('\n📊 Tóm tắt kết quả 24h:');
  allResults.forEach(record => {
    const data24h = record.data['24h'];
    if (data24h && data24h.totalVol > 0) {
      console.log(`  ${record.symbol}: Vol=$${data24h.totalVol.toLocaleString()}, Txns=${data24h.totalTxns.toLocaleString()}, NetVol=$${data24h.netVol.toLocaleString()}`);
    } else {
      console.log(`  ${record.symbol}: Không có dữ liệu`);
    }
  });

  // Summary statistics
  const validResults = allResults.filter(r => r.data['24h'] && r.data['24h'].totalVol > 0);
  if (validResults.length > 0) {
    const totalVolume24h = validResults.reduce((sum, r) => sum + r.data['24h'].totalVol, 0);
    const totalTxns24h = validResults.reduce((sum, r) => sum + r.data['24h'].totalTxns, 0);
    const totalNetVol24h = validResults.reduce((sum, r) => sum + r.data['24h'].netVol, 0);
    
    console.log('\n📈 Thống kê tổng hợp 24h:');
    console.log(`  Tokens có dữ liệu: ${validResults.length}/${allResults.length}`);
    console.log(`  Tổng Volume: $${totalVolume24h.toLocaleString()}`);
    console.log(`  Tổng Transactions: ${totalTxns24h.toLocaleString()}`);
    console.log(`  Tổng Net Volume: $${totalNetVol24h.toLocaleString()}`);
  }
}

main();
