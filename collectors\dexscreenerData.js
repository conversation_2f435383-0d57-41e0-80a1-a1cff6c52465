/**
 * Simple one-command script: just run `node dexscreenerData.js`
 *
 * Gets token addresses from tokenList.js and fetches data for all tokens.
 * Automatically fetches search -> pair details -> saves selected fields.
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Import token list
const { tokenData } = require('../tokenList.js');

// === CONFIG ===
const CONFIG = {
  CHAIN: 'aptos',
  MAX_PAIRS_PER_TOKEN: 30, // Maximum pairs to fetch per token
  MAX_TOKENS: 30, // Maximum tokens to process (set to -1 for all tokens)
  OUT_FILE: '../results/dexscreenerData.json',
};

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

async function fetchSearch(token) {
  const url = `https://api.dexscreener.com/latest/dex/search?q=${encodeURIComponent(token)}`;
  const res = await axios.get(url, { headers: { accept: 'application/json' }, timeout: 30000 });
  const pairs = Array.isArray(res.data?.pairs) ? res.data.pairs : [];
  return pairs.map(p => ({ dexId: p.dexId, pairAddress: p.pairAddress, chainId: p.chainId }));
}

async function fetchPair(chain, pairAddress, retry = 0) {
  const url = `https://api.dexscreener.com/latest/dex/pairs/${encodeURIComponent(chain)}/${encodeURIComponent(pairAddress)}`;
  try {
    const res = await axios.get(url, { headers: { accept: 'application/json' }, timeout: 30000 });
    return res.data;
  } catch (err) {
    const status = err?.response?.status;
    if ((status === 429 || status >= 500 || !status) && retry < 4) {
      const backoff = 400 * Math.pow(2, retry);
      console.warn(`[${pairAddress}] ${status || 'NET'} -> retry in ${backoff}ms`);
      await sleep(backoff);
      return fetchPair(chain, pairAddress, retry + 1);
    }
    return { error: true, status, message: err?.message || 'request failed' };
  }
}

function selectFields(detail) {
  const src = detail?.pair || (Array.isArray(detail?.pairs) ? detail.pairs[0] : null);
  if (!src) return null;
  return {
    dexId: src.dexId ?? null,
    pairAddress: src.pairAddress ?? null,
    tokenAddress: src.baseToken?.address ?? null,
    tokenSymbol: src.baseToken?.symbol ?? null,
    tokenName: src.baseToken?.name ?? null,
    txns: src.txns ?? null,
    volume: src.volume ?? null,
    priceChange: src.priceChange ?? null,
    liquidityUsd: src.liquidity?.usd ?? null,
    marketCap: src.marketCap ?? null,
  };
}

(async () => {
  console.log(`Đang xử lý ${tokenData.length} token từ tokenList.js...`);
  
  const tokensToProcess = CONFIG.MAX_TOKENS === -1 ? tokenData : tokenData.slice(0, CONFIG.MAX_TOKENS);
  console.log(`Sẽ xử lý ${tokensToProcess.length} token đầu tiên.`);

  const allResults = [];
  
  for (let tokenIndex = 0; tokenIndex < tokensToProcess.length; tokenIndex++) {
    const token = tokensToProcess[tokenIndex];
    console.log(`\n[${tokenIndex + 1}/${tokensToProcess.length}] Xử lý token: ${token.symbol} (${token.name})`);
    console.log(`Token address: ${token.address}`);
    
    try {
      // Fetch search results for this token
      const pairs = await fetchSearch(token.address);
      if (!pairs.length) {
        console.log(`  Không tìm thấy pair nào cho token ${token.symbol}`);
        continue;
      }
      
      const targets = pairs.slice(0, CONFIG.MAX_PAIRS_PER_TOKEN);
      console.log(`  Tìm thấy ${pairs.length} pair(s). Lấy chi tiết ${targets.length} pair đầu.`);

      // Fetch details for each pair
      for (let i = 0; i < targets.length; i++) {
        const { pairAddress, chainId } = targets[i];
        const chain = CONFIG.CHAIN || chainId || 'aptos';
        console.log(`    [${i + 1}/${targets.length}] Fetch detail for ${chain}:${pairAddress}`);
        
        const detail = await fetchPair(chain, pairAddress);
        const picked = selectFields(detail);
        if (picked) {
          // Add source token info
          picked.sourceToken = {
            address: token.address,
            symbol: token.symbol,
            name: token.name
          };
          allResults.push(picked);
        }
        
        // Rate limiting
        await sleep(120);
      }
      
    } catch (error) {
      console.error(`  Lỗi khi xử lý token ${token.symbol}:`, error.message);
    }
    
    // Delay between tokens
    if (tokenIndex < tokensToProcess.length - 1) {
      await sleep(500);
    }
  }

  // Save results
  const outputPath = path.resolve(__dirname, CONFIG.OUT_FILE);
  fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2));
  console.log(`\nĐã lưu ${allResults.length} record vào ${outputPath}`);
  
  // Summary by token
  const summary = {};
  allResults.forEach(record => {
    const symbol = record.sourceToken.symbol;
    if (!summary[symbol]) summary[symbol] = 0;
    summary[symbol]++;
  });
  
  console.log('\nTóm tắt kết quả:');
  Object.entries(summary).forEach(([symbol, count]) => {
    console.log(`  ${symbol}: ${count} pair(s)`);
  });
})();
