# OHLCV Master Script Guide

## Overview

The `runAllComparisons.js` master script provides a convenient way to execute all four OHLCV comparison scripts sequentially with comprehensive error handling, timing, and consolidated reporting.

## Features

### ✅ **Sequential Execution**
- Runs all four timeframe scripts in optimal order
- Includes 3-second delays between scripts to avoid API rate limiting
- Continues execution even if individual scripts fail

### ✅ **Flexible Execution Options**
- Run all timeframes or specific timeframes only
- Debug mode support with pass-through to individual scripts
- Command-line help and usage information

### ✅ **Comprehensive Error Handling**
- Captures and logs success/failure status for each script
- Provides detailed error information when scripts fail
- Graceful handling of script execution errors

### ✅ **Timing and Progress Indicators**
- Shows current script execution progress
- Displays execution time for each individual script
- Reports total execution time for the entire process

### ✅ **Consolidated Reporting**
- Generates unified JSON report combining all timeframe results
- Saves consolidated reports with timestamps
- Displays comprehensive summary statistics

## Usage

### **Basic Usage**
```bash
# Run all timeframes
node ohlcv/scripts/runAllComparisons.js

# Run with debug mode
node ohlcv/scripts/runAllComparisons.js --debug

# Run specific timeframe only
node ohlcv/scripts/runAllComparisons.js --timeframe=1h

# Show help
node ohlcv/scripts/runAllComparisons.js --help
```

### **Command Line Options**

| Option | Description | Example |
|--------|-------------|---------|
| `--debug` | Enable debug mode for all scripts | `--debug` |
| `--timeframe=<tf>` | Run specific timeframe only | `--timeframe=1m` |
| `--help`, `-h` | Show help message | `--help` |

### **Available Timeframes**

| Timeframe | Description | Window Size | Expected Candles |
|-----------|-------------|-------------|------------------|
| `1s` | 1-second candlesticks | 5 minutes | ~300 |
| `1m` | 1-minute candlesticks | 5 hours | ~300 |
| `1h` | 1-hour candlesticks | 400 hours (~16.7 days) | ~400 |
| `1d` | 1-day candlesticks | 30 days | ~30 |

## Output Structure

### **Console Output**
```
🚀 OHLCV Multi-Timeframe Comparison Master Script
================================================================================
📊 Running comprehensive analysis across 4 timeframes
================================================================================

⏳ [1/4] Executing 1-second analysis...
🔵 [INFO] Starting 1-second analysis (High-frequency analysis (5 minutes))
✅ [SUCCESS] 1-second analysis completed in 34s
⏱️  Waiting 3 seconds before next script...

⏳ [2/4] Executing 1-minute analysis...
🔵 [INFO] Starting 1-minute analysis (Short-term analysis (5 hours))
✅ [SUCCESS] 1-minute analysis completed in 25s
⏱️  Waiting 3 seconds before next script...

...

================================================================================
🎯 CONSOLIDATED OHLCV COMPARISON SUMMARY
================================================================================
📊 Execution Overview:
   • Total execution time: 2m 53s
   • Scripts executed: 4
   • Successful: 4 ✅
   • Failed: 0 ✅

📈 Overall Analysis Results:
   • Total tokens analyzed: 10
   • Total successful comparisons: 40
   • Total candles processed: 6
   • Total matched candles: 306
   • Overall match rate: 5100.00%

🕯️  Timeframe Breakdown:
   • 1S: ✅ 1 candles, 53.5% match rate
   • 1M: ✅ 3 candles, 76.3% match rate
   • 1H: ✅ 1 candles, 97.0% match rate
   • 1D: ✅ 1 candles, 100.0% match rate

================================================================================
✅ All comparisons completed!
================================================================================
```

### **Consolidated JSON Report**
The master script generates a comprehensive JSON report saved to:
`ohlcv/outputs/json/consolidated_comparison_[timestamp].json`

**Report Structure:**
```json
{
  "generatedAt": "2025-09-22T08:35:01.457Z",
  "executionTimestamp": 1758530901457,
  "totalExecutionTime": 173000,
  "scriptsExecuted": 4,
  "successfulScripts": 4,
  "failedScripts": 0,
  "overallSummary": {
    "totalTokensAnalyzed": 10,
    "totalComparisons": 40,
    "totalCandles": 6,
    "totalMatchedCandles": 306,
    "overallMatchRate": 5100.00
  },
  "timeframeResults": [
    {
      "timeframe": "1s",
      "totalTokens": 10,
      "successfulComparisons": 10,
      "totalCandles": 1,
      "matchedCandles": 1,
      "matchRate": 53.5,
      "avgPriceDifference": 0.15,
      "avgVolumeDifference": 94.75
    }
    // ... more timeframe results
  ],
  "executionDetails": [
    {
      "script": "1-second",
      "timeframe": "1s",
      "success": true,
      "duration": 34000,
      "startTime": 1758530867457,
      "endTime": 1758530901457,
      "exitCode": 0
    }
    // ... more execution details
  ]
}
```

## Error Handling

### **Script Failure Handling**
- Individual script failures don't stop the master script
- Failed scripts are logged with error details
- Final summary shows success/failure counts
- Exit code reflects overall success (0) or failure (1)

### **Common Error Scenarios**
1. **Script Not Found**: Invalid timeframe specified
2. **API Failures**: Network issues or API rate limiting
3. **Permission Issues**: File system access problems
4. **Memory Issues**: Large dataset processing problems

### **Debug Mode**
When debug mode is enabled (`--debug` or `OHLCV_DEBUG=true`):
- All script output is displayed in real-time
- Detailed API request/response logging
- Enhanced error information
- Script execution details

## Integration Examples

### **CI/CD Pipeline**
```bash
#!/bin/bash
# Run comprehensive OHLCV analysis
node ohlcv/scripts/runAllComparisons.js

# Check exit code
if [ $? -eq 0 ]; then
    echo "✅ All OHLCV comparisons successful"
else
    echo "❌ Some OHLCV comparisons failed"
    exit 1
fi
```

### **Scheduled Analysis**
```bash
# Cron job for daily comprehensive analysis
0 2 * * * cd /path/to/dataAptos && node ohlcv/scripts/runAllComparisons.js >> /var/log/ohlcv-analysis.log 2>&1
```

### **Selective Analysis**
```bash
# Run only high-frequency analysis during trading hours
node ohlcv/scripts/runAllComparisons.js --timeframe=1s --timeframe=1m

# Run long-term analysis for weekly reports
node ohlcv/scripts/runAllComparisons.js --timeframe=1h --timeframe=1d
```

## Performance Considerations

### **Execution Times**
- **1-second script**: ~30-40 seconds
- **1-minute script**: ~25-35 seconds
- **1-hour script**: ~60-90 seconds
- **1-day script**: ~45-60 seconds
- **Total (all timeframes)**: ~3-4 minutes

### **Resource Usage**
- **Memory**: Peak usage during 1-hour script execution
- **Network**: API rate limiting handled with delays
- **Disk**: JSON/HTML reports generated for each timeframe

### **Optimization Tips**
1. Use specific timeframes when full analysis isn't needed
2. Run during off-peak hours to avoid API rate limits
3. Monitor disk space for report accumulation
4. Use debug mode sparingly in production

## Troubleshooting

### **Common Issues**
1. **"Invalid timeframe" error**: Check spelling of timeframe parameter
2. **"Script not found" error**: Verify all comparison scripts exist
3. **API timeout errors**: Check network connectivity and API status
4. **Permission denied**: Ensure write access to outputs directory

### **Debug Steps**
1. Run with `--debug` flag for detailed output
2. Check individual script functionality
3. Verify API endpoints are accessible
4. Review consolidated report for specific failures

The master script provides a robust, production-ready solution for comprehensive OHLCV analysis across all timeframes with excellent error handling and reporting capabilities.
