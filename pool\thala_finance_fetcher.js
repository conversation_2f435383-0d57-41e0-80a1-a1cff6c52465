#!/usr/bin/env node

/**
 * Thala Finance API Data Fetcher (Node.js Version)
 * 
 * This script fetches TVL (Total Value Locked) and volume data from the Thala Finance API
 * and saves the response to a JSON file with proper error handling and logging.
 */

const https = require('https');
const fs = require('fs').promises;
const path = require('path');

class Logger {
    constructor() {
        this.logFile = 'thala_finance_fetcher.log';
    }

    async log(level, message) {
        const timestamp = new Date().toISOString();
        const logMessage = `${timestamp} - ${level} - ${message}\n`;
        
        // Log to console
        console.log(`${timestamp} - ${level} - ${message}`);
        
        // Log to file
        try {
            await fs.appendFile(this.logFile, logMessage);
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    async info(message) {
        await this.log('INFO', message);
    }

    async error(message) {
        await this.log('ERROR', message);
    }

    async warning(message) {
        await this.log('WARNING', message);
    }
}

class ThalaFinanceAPI {
    constructor() {
        this.baseUrl = 'https://app.thala.fi/api/liquidity-pool';
        this.poolType = '0xce9e3b2437fd2cddc5c14f6c4259fc7d3cef160b820837591aa48170bb509368';
        this.headers = {
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
            'priority': 'u=1, i',
            'referer': 'https://app.thala.fi/pools/0xc3c4cbb3efcd3ec1b6679dc0ed45851486920dba0e86e612e80a79041a6cf1a3',
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
            'Cookie': '_ga=GA1.1.2043805561.1757478646; _ga_GNVVWBL3J9=GS2.1.s1758507680$o5$g1$t1758508025$j60$l0$h0'
        };
        this.logger = new Logger();
    }

    async fetchPoolData(timeout = 30000) {
        return new Promise((resolve, reject) => {
            const url = `${this.baseUrl}?pool-type=${this.poolType}`;
            
            this.logger.info(`Fetching data from: ${url}`);

            const options = {
                method: 'GET',
                headers: this.headers,
                timeout: timeout
            };

            const req = https.request(url, options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        if (res.statusCode !== 200) {
                            throw new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`);
                        }

                        const jsonData = JSON.parse(data);
                        this.logger.info('Successfully fetched pool data');

                        // Log key metrics
                        if (jsonData.data) {
                            const poolData = jsonData.data;
                            this.logger.info(`TVL: $${poolData.tvl?.toLocaleString() || 'N/A'}`);
                            this.logger.info(`24h Volume: $${poolData.volume1d?.toLocaleString() || 'N/A'}`);
                            this.logger.info(`24h Fees: $${poolData.fees1d?.toLocaleString() || 'N/A'}`);
                        }

                        resolve(jsonData);
                    } catch (error) {
                        this.logger.error(`Failed to parse JSON response: ${error.message}`);
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                this.logger.error(`Request error: ${error.message}`);
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                const error = new Error(`Request timed out after ${timeout}ms`);
                this.logger.error(error.message);
                reject(error);
            });

            req.setTimeout(timeout);
            req.end();
        });
    }

    async saveToFile(data, filename = null) {
        try {
            if (!filename) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                filename = `thala_finance_data_${timestamp}.json`;
            }

            // Ensure outputs directory exists
            const outputDir = 'outputs';
            try {
                await fs.access(outputDir);
            } catch {
                await fs.mkdir(outputDir, { recursive: true });
            }

            const filepath = path.join(outputDir, filename);
            await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf8');

            this.logger.info(`Data saved to: ${filepath}`);
            return true;
        } catch (error) {
            this.logger.error(`Failed to save data to file: ${error.message}`);
            return false;
        }
    }

    extractKeyMetrics(data) {
        try {
            const poolData = data.data || {};

            const metrics = {
                timestamp: new Date().toISOString(),
                tvl: poolData.tvl,
                volume_24h: poolData.volume1d,
                fees_24h: poolData.fees1d,
                balances: poolData.balances || [],
                apr_sources: poolData.apr || [],
                stake_ratio: poolData.stakeRatio,
                pool_type: poolData.metadata?.poolType,
                swap_fee: poolData.metadata?.swapFee,
                amp: poolData.metadata?.amp
            };

            return metrics;
        } catch (error) {
            this.logger.error(`Failed to extract key metrics: ${error.message}`);
            return {};
        }
    }
}

async function main() {
    const api = new ThalaFinanceAPI();
    
    try {
        await api.logger.info('Starting Thala Finance data fetch');

        // Fetch data
        const data = await api.fetchPoolData();

        // Save complete response
        const success = await api.saveToFile(data);

        if (success) {
            // Extract and save key metrics
            const metrics = api.extractKeyMetrics(data);
            if (Object.keys(metrics).length > 0) {
                await api.logger.info('Key metrics extracted successfully');
                
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                const metricsFilename = `thala_finance_metrics_${timestamp}.json`;
                await api.saveToFile(metrics, metricsFilename);
            }
        }

        await api.logger.info('Thala Finance data fetch completed successfully');
        process.exit(0);

    } catch (error) {
        await api.logger.error(`Failed to fetch data: ${error.message}`);
        process.exit(1);
    }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
    const logger = new Logger();
    await logger.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
    process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
    const logger = new Logger();
    await logger.error(`Uncaught Exception: ${error.message}`);
    process.exit(1);
});

// Run main function if this script is executed directly
if (require.main === module) {
    main();
}

module.exports = { ThalaFinanceAPI };
