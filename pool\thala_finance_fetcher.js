#!/usr/bin/env node

/**
 * Thala Finance API Data Fetcher
 *
 * <PERSON><PERSON>t để fetch TVL (Total Value Locked) và volume data từ Thala Finance API
 * và lưu response vào file JSON với error handling và logging đầy đủ.
 */

const https = require('https');
const fs = require('fs').promises;
const path = require('path');

class Logger {
    constructor() {
        this.logFile = 'thala_finance_fetcher.log';
    }

    async log(level, message) {
        const timestamp = new Date().toISOString();
        const logMessage = `${timestamp} - ${level} - ${message}\n`;
        
        // Log to console
        console.log(`${timestamp} - ${level} - ${message}`);
        
        // Log to file
        try {
            await fs.appendFile(this.logFile, logMessage);
        } catch (error) {
            console.error('Failed to write to log file:', error.message);
        }
    }

    async info(message) {
        await this.log('INFO', message);
    }

    async error(message) {
        await this.log('ERROR', message);
    }

    async warning(message) {
        await this.log('WARNING', message);
    }
}

class ThalaFinanceAPI {
    constructor() {
        this.baseUrl = 'https://app.thala.fi/api/liquidity-pool';
        this.poolType = '0xce9e3b2437fd2cddc5c14f6c4259fc7d3cef160b820837591aa48170bb509368';
        this.headers = {
            'accept': '*/*',
            'accept-language': 'en-US,en;q=0.9,vi;q=0.8',
            'priority': 'u=1, i',
            'referer': 'https://app.thala.fi/pools/0xc3c4cbb3efcd3ec1b6679dc0ed45851486920dba0e86e612e80a79041a6cf1a3',
            'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
            'Cookie': '_ga=GA1.1.2043805561.1757478646; _ga_GNVVWBL3J9=GS2.1.s1758507680$o5$g1$t1758508025$j60$l0$h0'
        };
        this.logger = new Logger();
    }

    async fetchPoolData(timeout = 30000) {
        return new Promise((resolve, reject) => {
            const url = `${this.baseUrl}?pool-type=${this.poolType}`;
            
            this.logger.info(`Đang fetch data từ: ${url}`);

            const options = {
                method: 'GET',
                headers: this.headers,
                timeout: timeout
            };

            const req = https.request(url, options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        if (res.statusCode !== 200) {
                            throw new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`);
                        }

                        const jsonData = JSON.parse(data);
                        this.logger.info('Fetch pool data thành công');

                        // Log key metrics
                        if (jsonData.data) {
                            const poolData = jsonData.data;
                            this.logger.info(`TVL: $${poolData.tvl?.toLocaleString() || 'N/A'}`);
                            this.logger.info(`24h Volume: $${poolData.volume1d?.toLocaleString() || 'N/A'}`);
                            this.logger.info(`24h Fees: $${poolData.fees1d?.toLocaleString() || 'N/A'}`);
                        }

                        resolve(jsonData);
                    } catch (error) {
                        this.logger.error(`Lỗi parse JSON response: ${error.message}`);
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                this.logger.error(`Request error: ${error.message}`);
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                const error = new Error(`Request timeout sau ${timeout}ms`);
                this.logger.error(error.message);
                reject(error);
            });

            req.setTimeout(timeout);
            req.end();
        });
    }

    async saveToFile(data, filename = null) {
        try {
            if (!filename) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                filename = `thala_finance_data_${timestamp}.json`;
            }

            // Tạo outputs directory nếu chưa có
            const outputDir = 'outputs';
            try {
                await fs.access(outputDir);
            } catch {
                await fs.mkdir(outputDir, { recursive: true });
                this.logger.info(`Đã tạo thư mục: ${outputDir}`);
            }

            const filepath = path.join(outputDir, filename);
            await fs.writeFile(filepath, JSON.stringify(data, null, 2), 'utf8');

            this.logger.info(`Data đã được lưu vào: ${filepath}`);
            return true;
        } catch (error) {
            this.logger.error(`Lỗi khi lưu file: ${error.message}`);
            return false;
        }
    }

    extractKeyMetrics(data) {
        try {
            const poolData = data.data || {};

            const metrics = {
                timestamp: new Date().toISOString(),
                tvl: poolData.tvl,
                volume_24h: poolData.volume1d,
                fees_24h: poolData.fees1d,
                balances: poolData.balances || [],
                apr_sources: poolData.apr || [],
                stake_ratio: poolData.stakeRatio,
                pool_type: poolData.metadata?.poolType,
                swap_fee: poolData.metadata?.swapFee,
                amp: poolData.metadata?.amp,
                num_coins: poolData.metadata?.numCoins,
                coin_addresses: poolData.metadata?.coinAddresses || [],
                lpt_address: poolData.metadata?.lptAddress
            };

            return metrics;
        } catch (error) {
            this.logger.error(`Lỗi khi trích xuất key metrics: ${error.message}`);
            return {};
        }
    }

    /**
     * Log chi tiết về pool data
     * @param {Object} data - API response data
     */
    async logPoolDetails(data) {
        try {
            if (!data.data) return;

            const poolData = data.data;
            const metadata = poolData.metadata || {};

            await this.logger.info('=== THALA FINANCE POOL DETAILS ===');
            await this.logger.info(`Pool Type: ${metadata.poolType || 'N/A'}`);
            await this.logger.info(`Number of Coins: ${metadata.numCoins || 'N/A'}`);
            await this.logger.info(`Swap Fee: ${(metadata.swapFee * 100)?.toFixed(2) || 'N/A'}%`);
            await this.logger.info(`Amplification: ${metadata.amp || 'N/A'}`);
            await this.logger.info(`Version: ${metadata.version || 'N/A'}`);
            await this.logger.info(`Is V2: ${metadata.isV2 || 'N/A'}`);

            if (poolData.apr && poolData.apr.length > 0) {
                await this.logger.info('APR Sources:');
                for (const apr of poolData.apr) {
                    await this.logger.info(`  - ${apr.source}: ${(apr.apr * 100).toFixed(2)}%`);
                }
            }

            await this.logger.info('=====================================');
        } catch (error) {
            await this.logger.error(`Lỗi khi log pool details: ${error.message}`);
        }
    }
}

async function main() {
    const api = new ThalaFinanceAPI();
    
    try {
        await api.logger.info('🚀 Bắt đầu fetch Thala Finance data');

        // Fetch data từ API
        const data = await api.fetchPoolData();

        // Log chi tiết về pool
        await api.logPoolDetails(data);

        // Lưu complete response
        const success = await api.saveToFile(data);

        if (success) {
            // Trích xuất và lưu key metrics
            const metrics = api.extractKeyMetrics(data);
            if (Object.keys(metrics).length > 0) {
                await api.logger.info('✅ Key metrics đã được trích xuất thành công');

                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                const metricsFilename = `thala_finance_metrics_${timestamp}.json`;
                await api.saveToFile(metrics, metricsFilename);
            }
        }

        await api.logger.info('🎉 Hoàn thành fetch Thala Finance data');
        process.exit(0);

    } catch (error) {
        await api.logger.error(`❌ Lỗi khi fetch data: ${error.message}`);
        process.exit(1);
    }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
    const logger = new Logger();
    await logger.error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
    process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
    const logger = new Logger();
    await logger.error(`Uncaught Exception: ${error.message}`);
    process.exit(1);
});

// Chạy main function nếu script được execute trực tiếp
if (require.main === module) {
    main();
}

module.exports = { ThalaFinanceAPI };
