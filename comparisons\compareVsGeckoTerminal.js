/**
 * Compare token metrics between Lamboo and GeckoTerminal data sources
 * Usage: node compareVsGeckoTerminal.js
 * 
 * Compares data from:
 * - lambooMarketData.json (from Lamboo API)
 * - GeckoTerminalData.json (from GeckoTerminal API)
 * 
 * Output files:
 * - compareVsGeckoTerminal.json (detailed comparison)
 * - compareVsGeckoTerminal.csv (summary table)
 */

const fs = require('fs');
const path = require('path');

// === HELPER FUNCTIONS ===
const toNum = (x) => (x === null || x === undefined || x === '' ? null : Number(x));

function loadJSON(filePath) {
  try {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
  } catch (err) {
    console.error(`Error loading ${filePath}:`, err.message);
    return [];
  }
}

// Calculate percentage difference based on GeckoTerminal: (lamboo - gecko) / gecko * 100
// Positive = Lamboo higher (green), Negative = Lamboo lower (red)
function pctDiff(geckoVal, lambooVal) {
  const g = toNum(geckoVal) || 0;
  const l = toNum(lambooVal) || 0;
  const EPS = 1e-6;

  if (Math.abs(g) < EPS) return Math.abs(l) < EPS ? 0 : null;
  return ((l - g) / Math.abs(g)) * 100;
}

// Build CSV content
function toCSV(rows) {
  const header = [
    'addressToken', 'symbol', 'tokenName', 'poolAddress', 'poolName', 'timeframe',
    'gecko_volume', 'lamboo_volume', 'volume_delta', 'volume_pct',
    'gecko_swaps', 'lamboo_txns', 'swaps_delta', 'swaps_pct',
    'gecko_buyers', 'lamboo_buyers', 'buyers_delta', 'buyers_pct',
    'gecko_sellers', 'lamboo_sellers', 'sellers_delta', 'sellers_pct',
    'gecko_buySwaps', 'lamboo_buys', 'buySwaps_delta', 'buySwaps_pct',
    'gecko_sellSwaps', 'lamboo_sells', 'sellSwaps_delta', 'sellSwaps_pct',
    'gecko_priceUsd', 'lamboo_priceChange', 'status'
  ];
  
  const esc = (v) => {
    if (v === null || v === undefined) return '';
    const s = String(v);
    return /[",\n]/.test(s) ? '"' + s.replace(/"/g, '""') + '"' : s;
  };
  
  const pctFmt = (v) => (v === null || v === undefined || !Number.isFinite(v) ? '' : v.toFixed(2));
  
  const lines = [header.join(',')];
  
  for (const r of rows) {
    for (const tf of r.timeframes) {
      lines.push([
        esc(r.tokenAddress), esc(r.symbol), esc(r.tokenName), 
        esc(r.poolAddress), esc(r.poolName), esc(tf.timeframe),
        esc(tf.gecko?.volume), esc(tf.lamboo?.volume), esc(tf.delta?.volume), esc(pctFmt(tf.pct?.volume)),
        esc(tf.gecko?.swaps), esc(tf.lamboo?.txns), esc(tf.delta?.swaps), esc(pctFmt(tf.pct?.swaps)),
        esc(tf.gecko?.buyers), esc(tf.lamboo?.buyers), esc(tf.delta?.buyers), esc(pctFmt(tf.pct?.buyers)),
        esc(tf.gecko?.sellers), esc(tf.lamboo?.sellers), esc(tf.delta?.sellers), esc(pctFmt(tf.pct?.sellers)),
        esc(tf.gecko?.buySwaps), esc(tf.lamboo?.buys), esc(tf.delta?.buySwaps), esc(pctFmt(tf.pct?.buySwaps)),
        esc(tf.gecko?.sellSwaps), esc(tf.lamboo?.sells), esc(tf.delta?.sellSwaps), esc(pctFmt(tf.pct?.sellSwaps)),
        esc(tf.gecko?.priceUsd), esc(tf.lamboo?.priceChange), esc(tf.status)
      ].join(','));
    }
  }
  
  return lines.join('\n');
}

// Calculate timeframe statistics for HTML
function calculateTimeframeStats(rows) {
  const timeframeStats = {
    '5m': { total: 0, match: 0, good: 0, mismatch: 0 },
    '1h': { total: 0, match: 0, good: 0, mismatch: 0 },
    '24h': { total: 0, match: 0, good: 0, mismatch: 0 }
  };

  rows.forEach(token => {
    token.timeframes.forEach(tf => {
      if (timeframeStats[tf.timeframe]) {
        timeframeStats[tf.timeframe].total++;
        if (tf.status === 'MATCH') timeframeStats[tf.timeframe].match++;
        else if (tf.status === 'GOOD') timeframeStats[tf.timeframe].good++;
        else if (tf.status === 'MISMATCH') timeframeStats[tf.timeframe].mismatch++;
      }
    });
  });

  return timeframeStats;
}

// Generate HTML for timeframe statistics
function generateTimeframeStatsHTML(timeframeStats) {
  let html = `
        <table style="width: 100%; margin-top: 10px;">
            <thead>
                <tr>
                    <th>Timeframe</th>
                    <th>Total</th>
                    <th>Match</th>
                    <th>Good</th>
                    <th>Mismatch</th>
                </tr>
            </thead>
            <tbody>`;

  Object.keys(timeframeStats).forEach(tf => {
    const stats = timeframeStats[tf];
    if (stats.total > 0) {
      html += `
                <tr>
                    <td><strong>${tf.toUpperCase()}</strong></td>
                    <td class="number">${stats.total}</td>
                    <td class="number"><span class="match" style="padding: 2px 6px; border-radius: 3px;">${stats.match} (${(stats.match/stats.total*100).toFixed(1)}%)</span></td>
                    <td class="number"><span class="good" style="padding: 2px 6px; border-radius: 3px;">${stats.good} (${(stats.good/stats.total*100).toFixed(1)}%)</span></td>
                    <td class="number"><span class="mismatch" style="padding: 2px 6px; border-radius: 3px;">${stats.mismatch} (${(stats.mismatch/stats.total*100).toFixed(1)}%)</span></td>
                </tr>`;
    }
  });

  html += `
            </tbody>
        </table>`;
  return html;
}

// Build HTML table with colors
function toHTML(rows) {
  const getStatusColor = (status) => {
    switch (status) {
      case 'MATCH': return '#d4edda'; // Light green
      case 'GOOD': return '#d1ecf1'; // Light blue
      case 'MISMATCH': return '#f8d7da'; // Light red
      case 'UNMATCHED': return '#fff3cd'; // Light yellow
      default: return '#ffffff';
    }
  };

  const getStatusTextColor = (status) => {
    switch (status) {
      case 'MATCH': return '#155724'; // Dark green
      case 'GOOD': return '#0c5460'; // Dark blue
      case 'MISMATCH': return '#721c24'; // Dark red
      case 'UNMATCHED': return '#856404'; // Dark yellow
      default: return '#000000';
    }
  };

  // Format percentage with color and sign
  const formatPctWithColor = (pct) => {
    if (pct === null || pct === undefined || !Number.isFinite(pct)) return 'N/A';

    const value = pct.toFixed(1);
    const sign = pct > 0 ? '+' : '';
    const color = pct > 0 ? '#28a745' : '#dc3545'; // Green for positive, red for negative

    return `<span style="color: ${color}; font-weight: bold;">${sign}${value}%</span>`;
  };

  let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>GeckoTerminal vs Lamboo Comparison</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', 'Segoe UI', 'SF Pro Display', system-ui, -apple-system, sans-serif;
            margin: 20px;
            background-color: #f8fafc;
            line-height: 1.5;
            color: #1a202c;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        th, td {
            border: 1px solid #e0e0e0;
            padding: 12px 10px;
            text-align: center;
            font-size: 13px;
            font-weight: 500;
        }
        th {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
            font-size: 12px;
            letter-spacing: 0.5px;
        }
        .status-cell { font-weight: bold; text-align: center; }
        .number {
            text-align: center;
            font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
            font-weight: 500;
            font-variant-numeric: tabular-nums;
            letter-spacing: 0.025em;
        }

        /* GeckoTerminal columns styling */
        .gecko-header {
            background: linear-gradient(135deg, #b45309 0%, #f59e0b 100%) !important;
            color: #fffbeb !important;
        }

        /* Lamboo columns styling */
        .lamboo-header {
            background: linear-gradient(135deg, #2d5016 0%, #38a169 100%) !important;
            color: #f0fff4 !important;
        }

        /* Delta/comparison columns styling */
        .delta-header {
            background: linear-gradient(135deg, #553c9a 0%, #7c3aed 100%) !important;
            color: #f3e8ff !important;
        }

        /* Token name styling */
        .token-name {
            text-align: left !important;
            font-weight: 600;
        }
        .token-name strong {
            font-size: 14px;
            color: #2d3748;
            display: block;
            margin-bottom: 2px;
        }
        .token-name small {
            font-size: 11px;
            color: #718096;
            font-weight: 400;
        }

        .pool-name {
            max-width: 150px;
            word-wrap: break-word;
            font-weight: 600;
            text-align: left;
        }
        .address {
            font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', monospace;
            font-size: 10px;
            word-break: break-all;
            white-space: normal;
            background-color: #edf2f7;
            padding: 4px 6px;
            border-radius: 4px;
            color: #4a5568;
            text-align: left;
            max-width: 200px;
        }
        .summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .summary h3 {
            margin-top: 0;
            color: #495057;
            font-size: 1.3em;
        }
        .match { background-color: #d4edda; color: #155724; }
        .good { background-color: #d1ecf1; color: #0c5460; }
        .mismatch { background-color: #f8d7da; color: #721c24; }
        .unmatched { background-color: #fff3cd; color: #856404; }

        /* Hover effects */
        tbody tr:hover {
            background-color: #f8f9fa !important;
            transform: scale(1.001);
            transition: all 0.2s ease;
        }

        /* Responsive design */
        @media (max-width: 1200px) {
            body { margin: 10px; }
            table { font-size: 11px; }
            th, td { padding: 6px 4px; }
        }
    </style>
</head>
<body>
    <h1>🔍 GeckoTerminal vs Lamboo Market Data Comparison</h1>
    <div class="summary">
        <h3>📊 Summary Statistics</h3>
        <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Tolerance:</strong> 10% for all metrics (volume, swaps, buyers, sellers, etc.)</p>
        <p><strong>Percentage Calculation:</strong> Based on GeckoTerminal data as reference. <span style="color: #28a745; font-weight: bold;">+Green</span> = Lamboo higher than GeckoTerminal, <span style="color: #dc3545; font-weight: bold;">-Red</span> = Lamboo lower than GeckoTerminal</p>
    </div>

    <div class="summary">
        <h3>📈 Statistics by Timeframe</h3>
        ${generateTimeframeStatsHTML(calculateTimeframeStats(rows))}
    </div>

    <table>
        <thead>
            <tr>
                <th>Token</th>
                <th>Pool Address</th>
                <th>Pool Name</th>
                <th>Timeframe</th>
                <th class="gecko-header">Gecko Volume</th>
                <th class="lamboo-header">Lamboo Volume</th>
                <th class="delta-header">Volume Δ%</th>
                <th class="gecko-header">Gecko Swaps</th>
                <th class="lamboo-header">Lamboo Txns</th>
                <th class="delta-header">Swaps Δ%</th>
                <th class="gecko-header">Gecko Buyers</th>
                <th class="lamboo-header">Lamboo Buyers</th>
                <th class="delta-header">Buyers Δ%</th>
                <th class="gecko-header">Gecko Sellers</th>
                <th class="lamboo-header">Lamboo Sellers</th>
                <th class="delta-header">Sellers Δ%</th>
                <th class="gecko-header">Gecko Buy Swaps</th>
                <th class="lamboo-header">Lamboo Buys</th>
                <th class="delta-header">Buy Swaps Δ%</th>
                <th class="gecko-header">Gecko Sell Swaps</th>
                <th class="lamboo-header">Lamboo Sells</th>
                <th class="delta-header">Sell Swaps Δ%</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>`;

  for (const r of rows) {
    for (const tf of r.timeframes) {
      const statusColor = getStatusColor(tf.status);
      const statusTextColor = getStatusTextColor(tf.status);

      const volumePct = formatPctWithColor(tf.pct?.volume);
      const swapsPct = formatPctWithColor(tf.pct?.swaps);
      const buyersPct = formatPctWithColor(tf.pct?.buyers);
      const sellersPct = formatPctWithColor(tf.pct?.sellers);
      const buySwapsPct = formatPctWithColor(tf.pct?.buySwaps);
      const sellSwapsPct = formatPctWithColor(tf.pct?.sellSwaps);

      html += `
            <tr style="background-color: ${statusColor};">
                <td class="token-name"><strong>${r.symbol}</strong><br><small>${r.tokenName}</small></td>
                <td class="address">${r.poolAddress || 'N/A'}</td>
                <td class="pool-name">${r.poolName || 'N/A'}</td>
                <td class="number"><strong>${tf.timeframe}</strong></td>
                <td class="number">$${tf.gecko?.volume?.toLocaleString() || 'N/A'}</td>
                <td class="number">$${tf.lamboo?.volume?.toLocaleString() || 'N/A'}</td>
                <td class="number">${volumePct}</td>
                <td class="number">${tf.gecko?.swaps?.toLocaleString() || 'N/A'}</td>
                <td class="number">${tf.lamboo?.txns?.toLocaleString() || 'N/A'}</td>
                <td class="number">${swapsPct}</td>
                <td class="number">${tf.gecko?.buyers || 'N/A'}</td>
                <td class="number">${tf.lamboo?.buyers || 'N/A'}</td>
                <td class="number">${buyersPct}</td>
                <td class="number">${tf.gecko?.sellers || 'N/A'}</td>
                <td class="number">${tf.lamboo?.sellers || 'N/A'}</td>
                <td class="number">${sellersPct}</td>
                <td class="number">${tf.gecko?.buySwaps?.toLocaleString() || 'N/A'}</td>
                <td class="number">${tf.lamboo?.buys?.toLocaleString() || 'N/A'}</td>
                <td class="number">${buySwapsPct}</td>
                <td class="number">${tf.gecko?.sellSwaps?.toLocaleString() || 'N/A'}</td>
                <td class="number">${tf.lamboo?.sells?.toLocaleString() || 'N/A'}</td>
                <td class="number">${sellSwapsPct}</td>
                <td class="status-cell" style="color: ${statusTextColor}; font-weight: bold;">${tf.status}</td>
            </tr>`;
    }
  }

  html += `
        </tbody>
    </table>
    <div class="summary">
        <h3>🎨 Color Legend</h3>
        <p><span class="match" style="padding: 5px 10px; border-radius: 3px;">MATCH</span> - Data matches within tolerance</p>
        <p><span class="good" style="padding: 5px 10px; border-radius: 3px;">GOOD</span> - Lamboo data is higher than GeckoTerminal (positive direction)</p>
        <p><span class="mismatch" style="padding: 5px 10px; border-radius: 3px;">MISMATCH</span> - Data differs significantly (negative direction)</p>
        <p><span class="unmatched" style="padding: 5px 10px; border-radius: 3px;">UNMATCHED</span> - Data only available from one source</p>
    </div>
</body>
</html>`;

  return html;
}

// === MAIN LOGIC ===
function main() {
  const __dirname = path.dirname(__filename);
  
  // Load data files
  const lambooDataRaw = loadJSON(path.join(__dirname, '../results/LambooMarketData.json'));
  const geckoData = loadJSON(path.join(__dirname, '../results/GeckoTerminalData.json'));

  // Filter out Lamboo pools with volume = 0
  const lambooData = lambooDataRaw.filter(pool => {
    const volume = pool.volume || 0;
    return volume > 0;
  });

  const filteredCount = lambooDataRaw.length - lambooData.length;
  console.log(`Loaded ${lambooDataRaw.length} Lamboo records and ${geckoData.length} GeckoTerminal records`);
  if (filteredCount > 0) {
    console.log(`Filtered out ${filteredCount} Lamboo pools with volume = 0`);
  }
  console.log(`Using ${lambooData.length} valid Lamboo pools for comparison`);
  
  // Create maps by pair address
  const lambooMap = new Map();
  lambooData.forEach(item => {
    const pairAddr = item.pair_address?.toLowerCase();
    if (pairAddr) {
      lambooMap.set(pairAddr, item);
    }
  });

  // GeckoTerminal data mapped by pool address
  const geckoMap = new Map();
  geckoData.forEach(pool => {
    const poolAddr = pool.address?.toLowerCase();
    if (poolAddr) {
      geckoMap.set(poolAddr, pool);
    }
  });

  // Get intersection of pair addresses (only compare pools that exist in both sources)
  const commonPairAddresses = new Set();
  for (const pairAddr of lambooMap.keys()) {
    if (geckoMap.has(pairAddr)) {
      commonPairAddresses.add(pairAddr);
    }
  }

  console.log(`Found ${lambooData.length} Lamboo pools and ${geckoData.length} GeckoTerminal pools`);
  console.log(`Found ${commonPairAddresses.size} common pair addresses to compare`);
  
  const results = [];
  const tolerance = 10; // 10% tolerance for volume comparison
  
  for (const pairAddress of commonPairAddresses) {
    const lambooPool = lambooMap.get(pairAddress);
    const geckoPool = geckoMap.get(pairAddress);

    const symbol = lambooPool?.sourceToken?.symbol || geckoPool?.sourceToken?.symbol || 'UNKNOWN';
    const tokenName = lambooPool?.sourceToken?.name || geckoPool?.sourceToken?.name || 'Unknown';
    const poolName = geckoPool?.name || 'Unknown Pool';

    console.log(`\nProcessing ${symbol} (${tokenName}) - Pool: ${poolName}`);
    console.log(`  Pair Address: ${pairAddress}`);
    
    const poolResult = {
      tokenAddress: lambooPool?.sourceToken?.address || geckoPool?.sourceToken?.address,
      symbol,
      tokenName,
      poolAddress: pairAddress,
      poolName: poolName,
      timeframes: []
    };

    // Compare 24h timeframe (only timeframe available in both sources)
    const geckoTf = geckoPool.data?.['24h'];
    const lambooTf = lambooPool; // LambooMarketData is already 24h data

    if (!geckoTf && !lambooTf) {
      console.log(`    24h: No data from both sources`);
      continue;
    }

    // Extract metrics
    const geckoMetrics = geckoTf ? {
      volume: geckoTf.volume || 0,
      swaps: geckoTf.swaps || 0,
      buyers: geckoTf.buyers || 0,
      sellers: geckoTf.sellers || 0,
      buySwaps: geckoTf.buySwaps || 0,
      sellSwaps: geckoTf.sellSwaps || 0,
      priceUsd: geckoTf.priceUsd || 0
    } : null;

    const lambooMetrics = lambooTf ? {
      volume: lambooTf.volume || 0,
      txns: lambooTf.txns || 0,
      buyers: lambooTf.buyers || 0,
      sellers: lambooTf.sellers || 0,
      buys: lambooTf.buys || 0,
      sells: lambooTf.sells || 0,
      priceChange: 0 // LambooMarketData doesn't have price change
    } : null;

    // Skip if all GeckoTerminal data is null/undefined (no meaningful comparison possible)
    if (!geckoMetrics || (geckoMetrics.volume === 0 && geckoMetrics.swaps === 0 && geckoMetrics.buyers === 0)) {
      console.log(`    24h: Skipping - No meaningful GeckoTerminal data available`);
      continue;
    }

    // Calculate deltas and percentages
    const delta = {
      volume: geckoMetrics && lambooMetrics ? geckoMetrics.volume - lambooMetrics.volume : null,
      swaps: geckoMetrics && lambooMetrics ? geckoMetrics.swaps - lambooMetrics.txns : null,
      buyers: geckoMetrics && lambooMetrics ? geckoMetrics.buyers - lambooMetrics.buyers : null,
      sellers: geckoMetrics && lambooMetrics ? geckoMetrics.sellers - lambooMetrics.sellers : null,
      buySwaps: geckoMetrics && lambooMetrics ? geckoMetrics.buySwaps - lambooMetrics.buys : null,
      sellSwaps: geckoMetrics && lambooMetrics ? geckoMetrics.sellSwaps - lambooMetrics.sells : null
    };

    const pct = {
      volume: geckoMetrics && lambooMetrics ? pctDiff(geckoMetrics.volume, lambooMetrics.volume) : null,
      swaps: geckoMetrics && lambooMetrics ? pctDiff(geckoMetrics.swaps, lambooMetrics.txns) : null,
      buyers: geckoMetrics && lambooMetrics ? pctDiff(geckoMetrics.buyers, lambooMetrics.buyers) : null,
      sellers: geckoMetrics && lambooMetrics ? pctDiff(geckoMetrics.sellers, lambooMetrics.sellers) : null,
      buySwaps: geckoMetrics && lambooMetrics ? pctDiff(geckoMetrics.buySwaps, lambooMetrics.buys) : null,
      sellSwaps: geckoMetrics && lambooMetrics ? pctDiff(geckoMetrics.sellSwaps, lambooMetrics.sells) : null
    };

    // Determine status
    let status = 'UNMATCHED';
    if (geckoMetrics && lambooMetrics) {
      const metrics = ['volume', 'swaps', 'buyers', 'sellers', 'buySwaps', 'sellSwaps'];
      const withinTolerance = metrics.every((metric) => {
        const pctVal = pct[metric];
        return Number.isFinite(pctVal) && Math.abs(pctVal) <= tolerance;
      });

      if (withinTolerance) {
        status = 'MATCH';
      } else {
        const lambooHigherVol = Number.isFinite(pct.volume) && pct.volume > 0; // Lamboo volume > GeckoTerminal volume
        const lambooHigherSwaps = Number.isFinite(pct.swaps) && pct.swaps > 0; // Lamboo swaps > GeckoTerminal swaps

        // If both volume and swaps are higher in Lamboo, it's a positive mismatch
        if (lambooHigherVol && lambooHigherSwaps) {
          status = 'GOOD';
        } else {
          status = 'MISMATCH';
        }
      }
    }

    console.log(`    24h: ${status}`);

    poolResult.timeframes.push({
      timeframe: '24h',
      gecko: geckoMetrics,
      lamboo: lambooMetrics,
      delta,
      pct,
      status
    });

    results.push(poolResult);
  }
  
  // Sort pools by Lamboo volume descending for consistent presentation
  results.sort((a, b) => {
    const volumeOf = (item) => {
      const timeframe = item?.timeframes?.[0];
      const volume = Number(timeframe?.lamboo?.volume);
      return Number.isFinite(volume) ? volume : -Infinity;
    };
    return volumeOf(b) - volumeOf(a);
  });

  // Save JSON report
  const jsonOutput = {
    generatedAt: new Date().toISOString(),
    tolerancePercentVolume: tolerance,
    notes: 'Comparison between GeckoTerminal and Lamboo token metrics. All metrics tolerance: 10% based on percentage differences.',
    pools: results
  };
  
  const jsonPath = path.join(__dirname, '../results/compareVsGeckoTerminal.json');
  fs.writeFileSync(jsonPath, JSON.stringify(jsonOutput, null, 2));
  console.log(`\n✅ Saved JSON report to ${jsonPath}`);

  // Save CSV summary
  const csvContent = toCSV(results);
  const csvPath = path.join(__dirname, '../reports/compareVsGeckoTerminal.csv');
  fs.writeFileSync(csvPath, csvContent, 'utf8');
  console.log(`✅ Saved CSV summary to ${csvPath}`);

  // Save HTML report with colors
  const htmlContent = toHTML(results);
  const htmlPath = path.join(__dirname, '../reports/compareVsGeckoTerminal.html');
  fs.writeFileSync(htmlPath, htmlContent, 'utf8');
  console.log(`✅ Saved HTML report to ${htmlPath}`);
  
  // Summary statistics
  let totalComparisons = 0;
  let matchCount = 0;
  let goodCount = 0;
  let mismatchCount = 0;
  // Statistics by timeframe
  const timeframeStats = {
    '5m': { total: 0, match: 0, good: 0, mismatch: 0 },
    '1h': { total: 0, match: 0, good: 0, mismatch: 0 },
    '24h': { total: 0, match: 0, good: 0, mismatch: 0 }
  };

  results.forEach(pool => {
    pool.timeframes.forEach(tf => {
      totalComparisons++;

      // Update overall stats
      if (tf.status === 'MATCH') matchCount++;
      else if (tf.status === 'GOOD') goodCount++;
      else if (tf.status === 'MISMATCH') mismatchCount++;

      // Update timeframe stats
      if (timeframeStats[tf.timeframe]) {
        timeframeStats[tf.timeframe].total++;
        if (tf.status === 'MATCH') timeframeStats[tf.timeframe].match++;
        else if (tf.status === 'GOOD') timeframeStats[tf.timeframe].good++;
        else if (tf.status === 'MISMATCH') timeframeStats[tf.timeframe].mismatch++;
      }
    });
  });

  console.log('\n📊 Overall Summary Statistics:');
  console.log(`  Total comparisons: ${totalComparisons}`);
  console.log(`  Matches: ${matchCount} (${(matchCount/totalComparisons*100).toFixed(1)}%)`);
  console.log(`  Good (Lamboo higher): ${goodCount} (${(goodCount/totalComparisons*100).toFixed(1)}%)`);
  console.log(`  Mismatches: ${mismatchCount} (${(mismatchCount/totalComparisons*100).toFixed(1)}%)`);

  console.log('\n📈 Statistics by Timeframe:');
  Object.keys(timeframeStats).forEach(tf => {
    const stats = timeframeStats[tf];
    if (stats.total > 0) {
      console.log(`  ${tf.toUpperCase()}:`);
      console.log(`    Total: ${stats.total}`);
      console.log(`    Match: ${stats.match} (${(stats.match/stats.total*100).toFixed(1)}%)`);
      console.log(`    Good: ${stats.good} (${(stats.good/stats.total*100).toFixed(1)}%)`);
      console.log(`    Mismatch: ${stats.mismatch} (${(stats.mismatch/stats.total*100).toFixed(1)}%)`);
    }
  });
}

main();

