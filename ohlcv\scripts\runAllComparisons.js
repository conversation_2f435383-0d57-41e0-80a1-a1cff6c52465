#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// =============== CONFIGURATION ===============
const SCRIPTS = [
  { name: '1-second', file: 'compare1s.js', timeframe: '1s', description: 'High-frequency analysis (5 minutes)' },
  { name: '1-minute', file: 'compare1m.js', timeframe: '1m', description: 'Short-term analysis (5 hours)' },
  { name: '1-hour', file: 'compare1h.js', timeframe: '1h', description: 'Medium-term analysis (400 hours)' },
  { name: '1-day', file: 'compare1d.js', timeframe: '1d', description: 'Long-term analysis (30 days)' }
];

const DEBUG_MODE = process.env.OHLCV_DEBUG === 'true' || process.argv.includes('--debug');
const SCRIPT_DIR = __dirname;
const OUTPUT_DIR = path.join(__dirname, '..', 'outputs');

// =============== UTILITY FUNCTIONS ===============
function logInfo(message) {
  console.log(`🔵 [INFO] ${message}`);
}

function logSuccess(message) {
  console.log(`✅ [SUCCESS] ${message}`);
}

function logError(message) {
  console.log(`❌ [ERROR] ${message}`);
}

function logDebug(message) {
  if (DEBUG_MODE) {
    console.log(`🐛 [DEBUG] ${message}`);
  }
}

function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

function parseCommandLineArgs() {
  const args = process.argv.slice(2);
  const options = {
    debug: args.includes('--debug'),
    timeframe: null,
    help: args.includes('--help') || args.includes('-h')
  };
  
  // Parse timeframe option
  const timeframeArg = args.find(arg => arg.startsWith('--timeframe='));
  if (timeframeArg) {
    options.timeframe = timeframeArg.split('=')[1];
  }
  
  return options;
}

function showHelp() {
  console.log(`
🚀 OHLCV Comparison Master Script

Usage: node runAllComparisons.js [options]

Options:
  --debug                 Enable debug mode for all scripts
  --timeframe=<tf>        Run specific timeframe only (1s, 1m, 1h, 1d)
  --help, -h             Show this help message

Examples:
  node runAllComparisons.js                    # Run all timeframes
  node runAllComparisons.js --debug            # Run all with debug mode
  node runAllComparisons.js --timeframe=1h     # Run only 1-hour analysis
  node runAllComparisons.js --timeframe=1m --debug  # Run 1-minute with debug

Available Timeframes:
  1s - 1-second candlesticks (5 minutes window)
  1m - 1-minute candlesticks (5 hours window)  
  1h - 1-hour candlesticks (400 hours window)
  1d - 1-day candlesticks (30 days window)
`);
}

// =============== SCRIPT EXECUTION ===============
async function runScript(script, options = {}) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    logInfo(`Starting ${script.name} analysis (${script.description})`);
    
    const args = [];
    if (options.debug || DEBUG_MODE) {
      args.push('--debug');
    }
    
    const scriptPath = path.join(SCRIPT_DIR, script.file);
    logDebug(`Executing: node ${scriptPath} ${args.join(' ')}`);
    
    const child = spawn('node', [scriptPath, ...args], {
      stdio: 'pipe',
      cwd: path.dirname(scriptPath)
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      if (options.debug || DEBUG_MODE) {
        process.stdout.write(output);
      }
    });
    
    child.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      if (options.debug || DEBUG_MODE) {
        process.stderr.write(output);
      }
    });
    
    child.on('close', (code) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const result = {
        script: script.name,
        timeframe: script.timeframe,
        success: code === 0,
        duration,
        startTime,
        endTime,
        stdout,
        stderr,
        exitCode: code
      };
      
      if (code === 0) {
        logSuccess(`${script.name} analysis completed in ${formatDuration(duration)}`);
      } else {
        logError(`${script.name} analysis failed (exit code: ${code}) after ${formatDuration(duration)}`);
        if (stderr && !DEBUG_MODE) {
          console.log(`Error output: ${stderr.substring(0, 500)}...`);
        }
      }
      
      resolve(result);
    });
    
    child.on('error', (error) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      logError(`Failed to start ${script.name} analysis: ${error.message}`);
      
      resolve({
        script: script.name,
        timeframe: script.timeframe,
        success: false,
        duration,
        startTime,
        endTime,
        error: error.message,
        exitCode: -1
      });
    });
  });
}

// =============== RESULT PROCESSING ===============
function extractResultsFromOutput(output, timeframe) {
  const results = {
    timeframe,
    totalTokens: 0,
    successfulComparisons: 0,
    failedComparisons: 0,
    totalCandles: 0,
    matchedCandles: 0,
    matchRate: 0,
    avgPriceDifference: 0,
    avgVolumeDifference: 0,
    significantDifferences: 0
  };
  
  try {
    // Extract summary statistics from output
    const lines = output.split('\n');
    
    for (const line of lines) {
      if (line.includes('Tổng số tokens:')) {
        results.totalTokens = parseInt(line.match(/\d+/)?.[0] || '0');
      }
      if (line.includes('So sánh thành công:')) {
        results.successfulComparisons = parseInt(line.match(/\d+/)?.[0] || '0');
      }
      if (line.includes('Thất bại:')) {
        results.failedComparisons = parseInt(line.match(/\d+/)?.[0] || '0');
      }
      if (line.includes('Tổng số nến')) {
        results.totalCandles = parseInt(line.match(/\d+/)?.[0] || '0');
      }
      if (line.includes('Nến khớp:')) {
        results.matchedCandles = parseInt(line.match(/\d+/)?.[0] || '0');
      }
      if (line.includes('Tỷ lệ khớp:')) {
        const match = line.match(/([\d.]+)%/);
        results.matchRate = parseFloat(match?.[1] || '0');
      }
      if (line.includes('Chênh lệch giá trung bình:')) {
        const match = line.match(/([\d.]+)%/);
        results.avgPriceDifference = parseFloat(match?.[1] || '0');
      }
      if (line.includes('Chênh lệch volume trung bình:')) {
        const match = line.match(/([\d.]+)%/);
        results.avgVolumeDifference = parseFloat(match?.[1] || '0');
      }
      if (line.includes('chênh lệch đáng kể')) {
        results.significantDifferences = parseInt(line.match(/\d+/)?.[0] || '0');
      }
    }
  } catch (error) {
    logDebug(`Error parsing results for ${timeframe}: ${error.message}`);
  }
  
  return results;
}

function generateConsolidatedReport(executionResults) {
  const timestamp = Date.now();
  const consolidatedReport = {
    generatedAt: new Date().toISOString(),
    executionTimestamp: timestamp,
    totalExecutionTime: 0,
    scriptsExecuted: executionResults.length,
    successfulScripts: 0,
    failedScripts: 0,
    overallSummary: {
      totalTokensAnalyzed: 0,
      totalComparisons: 0,
      totalCandles: 0,
      totalMatchedCandles: 0,
      overallMatchRate: 0
    },
    timeframeResults: [],
    executionDetails: executionResults
  };
  
  let totalDuration = 0;
  let totalCandles = 0;
  let totalMatchedCandles = 0;
  let totalComparisons = 0;
  
  for (const result of executionResults) {
    totalDuration += result.duration;
    
    if (result.success) {
      consolidatedReport.successfulScripts++;
      const parsed = extractResultsFromOutput(result.stdout, result.timeframe);
      consolidatedReport.timeframeResults.push(parsed);
      
      totalCandles += parsed.totalCandles;
      totalMatchedCandles += parsed.matchedCandles;
      totalComparisons += parsed.successfulComparisons;
      consolidatedReport.overallSummary.totalTokensAnalyzed = Math.max(
        consolidatedReport.overallSummary.totalTokensAnalyzed, 
        parsed.totalTokens
      );
    } else {
      consolidatedReport.failedScripts++;
      consolidatedReport.timeframeResults.push({
        timeframe: result.timeframe,
        error: result.error || 'Script execution failed',
        exitCode: result.exitCode
      });
    }
  }
  
  consolidatedReport.totalExecutionTime = totalDuration;
  consolidatedReport.overallSummary.totalComparisons = totalComparisons;
  consolidatedReport.overallSummary.totalCandles = totalCandles;
  consolidatedReport.overallSummary.totalMatchedCandles = totalMatchedCandles;
  consolidatedReport.overallSummary.overallMatchRate = totalCandles > 0 ? 
    (totalMatchedCandles / totalCandles * 100) : 0;
  
  return consolidatedReport;
}

async function saveConsolidatedReport(report) {
  try {
    const jsonDir = path.join(OUTPUT_DIR, 'json');
    const filename = `consolidated_comparison.json`;
    const filepath = path.join(jsonDir, filename);

    // Ensure directory exists
    if (!fs.existsSync(jsonDir)) {
      fs.mkdirSync(jsonDir, { recursive: true });
    }

    fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
    logSuccess(`Consolidated report saved: ${filepath}`);
    return filepath;
  } catch (error) {
    logError(`Failed to save consolidated report: ${error.message}`);
    return null;
  }
}

// =============== SUMMARY DISPLAY ===============
function displayFinalSummary(report) {
  console.log('\n' + '='.repeat(80));
  console.log('🎯 CONSOLIDATED OHLCV COMPARISON SUMMARY');
  console.log('='.repeat(80));

  console.log(`📊 Execution Overview:`);
  console.log(`   • Total execution time: ${formatDuration(report.totalExecutionTime)}`);
  console.log(`   • Scripts executed: ${report.scriptsExecuted}`);
  console.log(`   • Successful: ${report.successfulScripts} ✅`);
  console.log(`   • Failed: ${report.failedScripts} ${report.failedScripts > 0 ? '❌' : '✅'}`);

  if (report.overallSummary.totalComparisons > 0) {
    console.log(`\n📈 Overall Analysis Results:`);
    console.log(`   • Total tokens analyzed: ${report.overallSummary.totalTokensAnalyzed}`);
    console.log(`   • Total successful comparisons: ${report.overallSummary.totalComparisons}`);
    console.log(`   • Total candles processed: ${report.overallSummary.totalCandles.toLocaleString()}`);
    console.log(`   • Total matched candles: ${report.overallSummary.totalMatchedCandles.toLocaleString()}`);
    console.log(`   • Overall match rate: ${report.overallSummary.overallMatchRate.toFixed(2)}%`);
  }

  console.log(`\n🕯️  Timeframe Breakdown:`);
  for (const result of report.timeframeResults) {
    if (result.error) {
      console.log(`   • ${result.timeframe.toUpperCase()}: ❌ Failed (${result.error})`);
    } else {
      console.log(`   • ${result.timeframe.toUpperCase()}: ✅ ${result.totalCandles.toLocaleString()} candles, ${result.matchRate.toFixed(1)}% match rate`);
    }
  }

  console.log('\n' + '='.repeat(80));
  console.log('✅ All comparisons completed!');
  console.log('='.repeat(80));
}

// =============== MAIN EXECUTION ===============
async function main() {
  const startTime = Date.now();
  const options = parseCommandLineArgs();

  if (options.help) {
    showHelp();
    return;
  }

  console.log('🚀 OHLCV Multi-Timeframe Comparison Master Script');
  console.log('=' .repeat(80));

  if (DEBUG_MODE || options.debug) {
    console.log('🐛 DEBUG MODE ENABLED - Detailed output will be shown');
  }

  // Determine which scripts to run
  let scriptsToRun = SCRIPTS;
  if (options.timeframe) {
    scriptsToRun = SCRIPTS.filter(script => script.timeframe === options.timeframe);
    if (scriptsToRun.length === 0) {
      logError(`Invalid timeframe: ${options.timeframe}. Available: ${SCRIPTS.map(s => s.timeframe).join(', ')}`);
      return;
    }
    console.log(`📊 Running single timeframe analysis: ${options.timeframe}`);
  } else {
    console.log(`📊 Running comprehensive analysis across ${SCRIPTS.length} timeframes`);
  }

  console.log('=' .repeat(80));

  // Execute scripts sequentially
  const executionResults = [];

  for (let i = 0; i < scriptsToRun.length; i++) {
    const script = scriptsToRun[i];
    console.log(`\n⏳ [${i + 1}/${scriptsToRun.length}] Executing ${script.name} analysis...`);

    const result = await runScript(script, {
      debug: options.debug || DEBUG_MODE
    });

    executionResults.push(result);

    // Add delay between scripts to avoid overwhelming the API
    if (i < scriptsToRun.length - 1) {
      console.log('⏱️  Waiting 3 seconds before next script...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  // Generate consolidated report
  console.log('\n📊 Generating consolidated report...');
  const consolidatedReport = generateConsolidatedReport(executionResults);
  const reportPath = await saveConsolidatedReport(consolidatedReport);

  // Display final summary
  displayFinalSummary(consolidatedReport);

  const totalTime = Date.now() - startTime;
  console.log(`\n🎉 Master script completed in ${formatDuration(totalTime)}`);

  if (reportPath) {
    console.log(`📄 Consolidated report: ${path.basename(reportPath)}`);
  }

  // Exit with appropriate code
  const hasFailures = consolidatedReport.failedScripts > 0;
  process.exit(hasFailures ? 1 : 0);
}

// =============== ERROR HANDLING ===============
process.on('uncaughtException', (error) => {
  logError(`Uncaught exception: ${error.message}`);
  console.error(error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// =============== SCRIPT ENTRY POINT ===============
if (require.main === module) {
  main().catch((error) => {
    logError(`Master script failed: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });
}

module.exports = {
  runScript,
  generateConsolidatedReport,
  saveConsolidatedReport,
  displayFinalSummary,
  SCRIPTS
};
