# 📊 Dex3 Token Data Collection & Comparison System

## 📁 Folder Structure

```
tests/dataAptos/
├── 📂 collectors/          # Data collection scripts
│   ├── dexscreenerData.js     # Collect from Dexscreener API
│   ├── LambooMarketData.js    # Collect pool data from Lamboo API
│   ├── lambooTokenData.js     # Collect token metrics from Lamboo API
│   ├── OKXMetricData.js       # Collect trading data from OKX API
│   └── GeckoTerminalData.js   # Collect pool data from GeckoTerminal API
│
├── 📂 comparisons/         # Data comparison scripts
│   ├── compareVsDexscreener.js # Compare Dexscreener vs Lamboo pools
│   ├── compareVsOKX.js        # Compare OKX vs Lamboo token metrics
│   └── compareVsGeckoTerminal.js # Compare GeckoTerminal vs Lamboo pools
│
├── 📂 results/             # Raw JSON data files
│   ├── dexscreenerData.json
│   ├── LambooMarketData.json
│   ├── lambooTokenData.json
│   ├── OKXMetricData.json
│   ├── GeckoTerminalData.json
│   ├── compareVsDexscreener.json
│   ├── compareVsOKX.json
│   └── compareVsGeckoTerminal.json
│
├── 📂 reports/             # Human-readable reports
│   ├── compareVsDexscreener.csv
│   ├── compareVsOKX.csv
│   ├── compareVsGeckoTerminal.csv
│   └── compareVsGeckoTerminal.html  # 🎨 Colored HTML report
│
└── tokenList.js            # Central token list (shared by all scripts)
```

## 🚀 Usage

### 1. Data Collection
```bash
# Collect data from different sources
cd collectors/
node dexscreenerData.js      # Dexscreener API
node LambooMarketData.js     # Lamboo pool data
node lambooTokenData.js      # Lamboo token metrics
node OKXMetricData.js        # OKX trading data
node GeckoTerminalData.js    # GeckoTerminal pool data
```

### 2. Data Comparison
```bash
# Compare data between sources
cd comparisons/
node compareVsDexscreener.js # Dexscreener vs Lamboo pools
node compareVsOKX.js         # OKX vs Lamboo token metrics
node compareVsGeckoTerminal.js # GeckoTerminal vs Lamboo pools
```

### 3. View Results
- **JSON files:** `results/` folder - Raw data for further processing
- **CSV files:** `reports/` folder - For Excel analysis
- **HTML file:** `reports/compareVsGeckoTerminal.html` - Visual report with colors

## 📊 Data Sources

| Source | Type | Timeframes | Coverage |
|--------|------|------------|----------|
| **Dexscreener** | Pool data | 24h | Multiple DEXes |
| **Lamboo Market** | Pool data | 24h | Aptos DEXes |
| **Lamboo Token** | Token metrics | 5m, 1h, 24h | Aggregated |
| **OKX** | Trading data | 5m, 1h, 24h | OKX coverage |
| **GeckoTerminal** | Pool data | 5m, 1h, 6h, 24h | Multiple DEXes |

## 🎯 Comparison Types

1. **Pool-level:** Compare specific pools between sources
2. **Token-level:** Compare aggregated token metrics
3. **Timeframe-based:** Compare across different time periods

## 🎨 Features

- ✅ **Automated data collection** from 5 different APIs
- ✅ **Rate limiting** to avoid API blocks
- ✅ **Error handling** with detailed logging
- ✅ **Multiple output formats** (JSON, CSV, HTML)
- ✅ **Color-coded HTML reports** for easy visualization
- ✅ **Percentage difference calculations**
- ✅ **Match/Mismatch status determination**
- ✅ **Organized folder structure**

## 🔧 Configuration

Each collector script has configurable parameters:
- `MAX_TOKENS`: Number of tokens to process
- `DELAY_BETWEEN_REQUESTS`: Rate limiting delay
- `TIMEOUT`: Request timeout
- `OUT_FILE`: Output file name

## 📈 Analysis

The system helps identify:
- **Data consistency** across different sources
- **Coverage gaps** between APIs
- **Volume/transaction discrepancies**
- **Price differences** between platforms
- **Reliability** of different data providers

## 🎯 Next Steps

1. **Automated scheduling** - Run collectors periodically
2. **Historical tracking** - Store data over time
3. **Alerting system** - Notify on significant discrepancies
4. **Dashboard** - Real-time data visualization
5. **API integration** - Expose comparison results via API
