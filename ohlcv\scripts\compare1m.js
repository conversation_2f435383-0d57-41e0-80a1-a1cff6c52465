/**
 * OHLCV Comparison - 1-Minute Timeframe
 * Lamboo V2 API vs OKX API
 *
 * Timeframe: 1-minute candlesticks
 * Analysis window: Most recent 5 hours, rounded down to nearest hour boundary
 * Expected data: ~300 candles (5 hours × 60 minutes)
 *
 * Usage: node ohlcv/scripts/compare1m.js
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { tokenData } = require('../../tokenList');

// =============== DEBUG CONFIGURATION ===============
const DEBUG_MODE = process.env.OHLCV_DEBUG === 'true' || process.argv.includes('--debug');

function logDebug(message) {
  if (DEBUG_MODE) {
    console.log(`🐛 [DEBUG] ${message}`);
  }
}

function generateCurlCommand(url, config) {
  if (!DEBUG_MODE) return;

  let curlCmd = `curl -X ${config.method || 'GET'} "${url}"`;

  // Add headers
  if (config.headers) {
    Object.entries(config.headers).forEach(([key, value]) => {
      curlCmd += ` \\\n  -H "${key}: ${value}"`;
    });
  }

  // Add query parameters
  if (config.params) {
    const urlObj = new URL(url);
    Object.entries(config.params).forEach(([key, value]) => {
      urlObj.searchParams.append(key, value);
    });
    curlCmd = curlCmd.replace(`"${url}"`, `"${urlObj.toString()}"`);
  }

  // Add request body
  if (config.data) {
    const bodyStr = typeof config.data === 'string' ? config.data : JSON.stringify(config.data, null, 2);
    curlCmd += ` \\\n  -d '${bodyStr}'`;
  }

  return curlCmd;
}

function logApiRequest(apiName, url, config) {
  if (!DEBUG_MODE) return;

  console.log(`\n🔍 [${apiName}] API Request Debug:`);
  console.log('=' .repeat(80));

  // Log request details
  console.log(`📡 Method: ${config.method || 'GET'}`);
  console.log(`🌐 URL: ${url}`);

  if (config.params) {
    console.log(`🔗 Query Params:`, JSON.stringify(config.params, null, 2));
  }

  if (config.headers) {
    console.log(`📋 Headers:`, JSON.stringify(config.headers, null, 2));
  }

  if (config.data) {
    console.log(`📦 Request Body:`, JSON.stringify(config.data, null, 2));
  }

  // Generate and log cURL command
  const curlCmd = generateCurlCommand(url, config);
  console.log(`\n💻 cURL Command:`);
  console.log(curlCmd);
  console.log('=' .repeat(80));
}

// =============== HELPER FUNCTIONS ===============
const toNum = (x) => (x === null || x === undefined || x === '' ? NaN : Number(x));
const isFiniteNum = (x) => Number.isFinite(toNum(x));

function fmtPrice(x) {
  const n = toNum(x);
  return isFiniteNum(n) ? n.toFixed(10) : "0.0000000000";
}

function toSec(ts) {
  const n = toNum(ts);
  if (!isFiniteNum(n)) return 0;
  return n >= 1e12 ? Math.floor(n / 1000) : Math.floor(n);
}

function calculatePercentageDifference(value1, value2) {
  if (value1 === 0 && value2 === 0) return 0;
  if (value1 === 0) return value2 > 0 ? 100 : -100;
  return ((value2 - value1) / Math.abs(value1)) * 100;
}

function arrayMapper(rows) {
  if (!Array.isArray(rows)) return [];
  return rows.map((r) => {
    if (!Array.isArray(r) || r.length < 5) return null;
    const ts = toSec(r[0]);
    const o = fmtPrice(r[1]);
    const h = fmtPrice(r[2]);
    const l = fmtPrice(r[3]);
    const c = fmtPrice(r[4]);
    const v = r.length >= 6 && isFiniteNum(r[5]) ? Number(r[5]) : 0;
    return [ts, o, h, l, c, v];
  }).filter(Boolean);
}

function findCandleRowsInJson(json) {
  let found = null;
  function dfs(node) {
    if (found) return;
    if (Array.isArray(node)) {
      if (node.length && Array.isArray(node[0]) && node[0].length >= 5) {
        found = { type: "array", rows: node }; return;
      }
      for (const it of node) dfs(it);
    } else if (node && typeof node === 'object') {
      for (const k of Object.keys(node)) dfs(node[k]);
    }
  }
  dfs(json);
  return found ? arrayMapper(found.rows) : [];
}

function parseRowsFromAPI(respJson, apiName = 'API') {
  if (DEBUG_MODE) {
    logDebug(`${apiName} Response Structure Analysis:`);
    logDebug(`Response type: ${typeof respJson}`);
    logDebug(`Response keys: ${respJson ? Object.keys(respJson).join(', ') : 'null'}`);

    if (respJson && typeof respJson === 'object') {
      if (respJson.data) {
        logDebug(`respJson.data type: ${typeof respJson.data}`);
        logDebug(`respJson.data keys: ${respJson.data ? Object.keys(respJson.data).join(', ') : 'null'}`);
        if (respJson.data.candles) {
          logDebug(`respJson.data.candles type: ${typeof respJson.data.candles}`);
          logDebug(`respJson.data.candles length: ${Array.isArray(respJson.data.candles) ? respJson.data.candles.length : 'not array'}`);
        }
      }

      // Log first few characters of response for structure analysis
      const jsonStr = JSON.stringify(respJson);
      logDebug(`Response preview (first 500 chars): ${jsonStr.substring(0, 500)}...`);
    }
  }

  const direct = respJson?.data?.candles ?? respJson?.data?.list ?? respJson?.result ??
                 (Array.isArray(respJson) ? respJson : null);

  if (Array.isArray(direct)) {
    if (DEBUG_MODE) logDebug(`${apiName} Using direct path, found ${direct.length} items`);
    return arrayMapper(direct);
  }

  if (DEBUG_MODE) logDebug(`${apiName} Using fallback search method`);
  return findCandleRowsInJson(respJson);
}

function formatNumber(num) {
  if (num === null || num === undefined || !Number.isFinite(num)) {
    return 'N/A';
  }
  return num.toLocaleString(undefined, { maximumFractionDigits: 6 });
}

function formatPercentage(pct) {
  if (pct === null || pct === undefined || !Number.isFinite(pct)) {
    return 'N/A';
  }
  return pct.toFixed(4) + '%';
}

function getStatusColor(avgPriceDiff, avgVolDiff) {
  const priceDiff = Math.abs(avgPriceDiff || 0);
  const volDiff = Math.abs(avgVolDiff || 0);
  
  if (priceDiff < 1 && volDiff < 50) return '#d4edda'; // Green - Excellent
  if (priceDiff < 5 && volDiff < 100) return '#d1ecf1'; // Blue - Good
  if (priceDiff < 10 && volDiff < 200) return '#fff3cd'; // Yellow - Fair
  return '#f8d7da'; // Red - Poor
}

function getStatusText(avgPriceDiff, avgVolDiff) {
  const priceDiff = Math.abs(avgPriceDiff || 0);
  const volDiff = Math.abs(avgVolDiff || 0);

  if (priceDiff < 1 && volDiff < 50) return 'EXCELLENT';
  if (priceDiff < 5 && volDiff < 100) return 'GOOD';
  if (priceDiff < 10 && volDiff < 200) return 'FAIR';
  return 'POOR';
}

// =============== TIMESTAMP FORMATTING ===============
function formatUTCTimestamp(unixTimestamp) {
  const date = new Date(unixTimestamp * 1000);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const seconds = date.getUTCSeconds().toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const year = date.getUTCFullYear();

  return `${hours}:${minutes}:${seconds} ${day}/${month}/${year} UTC`;
}

function formatReadableTimestamp(isoString) {
  const date = new Date(isoString);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const year = date.getUTCFullYear();

  return `${hours}:${minutes} ${day}/${month}/${year}`;
}

// =============== DISCREPANCY TABLE GENERATOR ===============
function generateDiscrepancyTable(discrepancies) {
  if (!discrepancies || discrepancies.length === 0) {
    return '';
  }

  let tableHtml = `
    <div style="margin: 30px 0;">
      <h3 style="color: #e74c3c; margin-bottom: 20px;">⚠️ Chênh lệch dữ liệu đáng kể (>5% OHLC)</h3>
      <p style="color: #7f8c8d; margin-bottom: 15px;">
        Hiển thị ${discrepancies.length} nến có chênh lệch giá cao nhất giữa 2 APIs:
      </p>
      <table style="font-size: 0.9em;">
        <thead>
          <tr>
            <th>Thời gian</th>
            <th>OKX Data<br><small>[timestamp, open, high, low, close, volume]</small></th>
            <th>Lamboo V2 Data<br><small>[timestamp, open, high, low, close, volume]</small></th>
            <th>Open Δ%</th>
            <th>High Δ%</th>
            <th>Low Δ%</th>
            <th>Close Δ%</th>
            <th>Volume Δ%</th>
          </tr>
        </thead>
        <tbody>`;

  discrepancies.forEach(discrepancy => {
    const okxData = discrepancy.okx;
    const lambooData = discrepancy.lamboo;
    const priceDiffs = discrepancy.priceDifferences;
    const volDiff = discrepancy.volumeDifference;

    // Helper function to get cell color based on difference
    const getCellColor = (diff) => {
      if (Math.abs(diff) < 1) return '#f8f9fa';
      if (diff > 0) return '#d4edda'; // Green if lamboo > okx
      return '#f8d7da'; // Red if lamboo < okx
    };

    const formatOHLCV = (data) => {
      if (!data) return 'N/A';
      // Preserve raw Unix timestamp in the data array
      return `[${data[0]}, ${parseFloat(data[1]).toFixed(6)}, ${parseFloat(data[2]).toFixed(6)}, ${parseFloat(data[3]).toFixed(6)}, ${parseFloat(data[4]).toFixed(6)}, ${data[5].toLocaleString()}]`;
    };

    tableHtml += `
      <tr>
        <td style="font-family: monospace; font-size: 0.8em;">${formatReadableTimestamp(discrepancy.timestamp)}</td>
        <td style="font-family: monospace; font-size: 0.75em; max-width: 200px; word-break: break-all;">${formatOHLCV(okxData)}</td>
        <td style="font-family: monospace; font-size: 0.75em; max-width: 200px; word-break: break-all;">${formatOHLCV(lambooData)}</td>
        <td style="background-color: ${getCellColor(priceDiffs.open)}; font-weight: bold;">${priceDiffs.open.toFixed(2)}%</td>
        <td style="background-color: ${getCellColor(priceDiffs.high)}; font-weight: bold;">${priceDiffs.high.toFixed(2)}%</td>
        <td style="background-color: ${getCellColor(priceDiffs.low)}; font-weight: bold;">${priceDiffs.low.toFixed(2)}%</td>
        <td style="background-color: ${getCellColor(priceDiffs.close)}; font-weight: bold;">${priceDiffs.close.toFixed(2)}%</td>
        <td style="background-color: ${getCellColor(volDiff)}; font-weight: bold;">${volDiff.toFixed(2)}%</td>
      </tr>`;
  });

  tableHtml += `
        </tbody>
      </table>
      <p style="color: #7f8c8d; font-size: 0.9em; margin-top: 15px;">
        <strong>Chú thích màu sắc:</strong>
        <span style="background-color: #d4edda; padding: 2px 6px; border-radius: 3px;">Xanh = Lamboo V2 > OKX</span>
        <span style="background-color: #f8d7da; padding: 2px 6px; border-radius: 3px; margin-left: 10px;">Đỏ = Lamboo V2 < OKX</span>
      </p>
    </div>`;

  return tableHtml;
}

// =============== HTTP REQUEST ===============
async function httpRequest(url, { method = "GET", params, data, headers, timeout = 20000 } = {}, apiName = 'API') {
  try {
    const config = { url, method, params, data, headers, timeout };
    logApiRequest(apiName, url, config);

    const res = await axios.request(config);

    if (DEBUG_MODE) {
      logDebug(`${apiName} Response Status: ${res.status}`);
      logDebug(`${apiName} Response Data Length: ${JSON.stringify(res.data).length} characters`);
    }

    return res.data;
  } catch (error) {
    if (DEBUG_MODE) {
      logDebug(`${apiName} Request Failed: ${error.message}`);
      if (error.response) {
        logDebug(`${apiName} Error Response Status: ${error.response.status}`);
        logDebug(`${apiName} Error Response Data: ${JSON.stringify(error.response.data)}`);
      }
    }

    throw new Error(`HTTP request failed: ${error.message}`);
  }
}

// =============== TIME CALCULATION FUNCTIONS ===============
function calculateRoundedTimeWindow5Hours() {
  const nowMs = Date.now();
  const nowDate = new Date(nowMs);

  // Round down to nearest hour boundary
  const roundedEndDate = new Date(nowDate.getFullYear(), nowDate.getMonth(), nowDate.getDate(), nowDate.getHours(), 0, 0, 0);
  const endTime = Math.floor(roundedEndDate.getTime() / 1000);

  // Calculate start time as exactly 5 hours before
  const startTime = endTime - (5 * 3600); // 5 hours = 5 * 3600 seconds

  return { startTime, endTime };
}

// =============== API FETCH FUNCTIONS ===============
async function fetchOKXData(tokenAddress, timeWindow = null) {
  const { startTime, endTime } = timeWindow || calculateRoundedTimeWindow5Hours();
  const interval = 60;
  const cb = 300;
  const to = endTime;
  const from = startTime;

  const config = {
    method: "POST",
    url: "https://api.lamboo.finance/v2/token-detail/ohlcv",
    params: { network: "aptos" },
    data: {
      network: "aptos", address: tokenAddress, timestamp: to, from, to,
      vsToken: "USDC", interval, cb, first: true, isMC: false,
      _: new Date(to * 1000).toISOString()
    },
    headers: {
      accept: "application/json", "content-type": "application/json",
      origin: "https://lamboo.finance", referer: "https://lamboo.finance/",
      clienttimestamp: String(Date.now())
    },
    timeout: 20000
  };

  const json = await httpRequest(config.url, config, 'OKX');
  return parseRowsFromAPI(json, 'OKX');
}

async function fetchLambooV2Data(tokenAddress, timeWindow = null) {
  const { startTime, endTime } = timeWindow || calculateRoundedTimeWindow5Hours();
  const interval = 60;
  const cb = 300;
  const to = endTime;
  const from = startTime;

  const config = {
    method: "POST",
    url: "https://api.lamboo.finance/v2/token-detail/ohlcv",
    params: { version: 2, network: "aptos" },
    data: {
      network: "aptos", address: tokenAddress, timestamp: to, from, to,
      vsToken: "USDC", interval, cb, first: true, isMC: false,
      _: new Date(to * 1000).toISOString()
    },
    headers: {
      accept: "application/json", "content-type": "application/json",
      origin: "https://lamboo.finance", referer: "https://lamboo.finance/",
      clienttimestamp: String(Date.now())
    },
    timeout: 20000
  };

  const json = await httpRequest(config.url, config, 'Lamboo V2');
  return parseRowsFromAPI(json, 'Lamboo V2');
}

// =============== DATA PROCESSING ===============
function alignCandlestickData(okxData, lambooData) {
  const aligned = [];
  const okxMap = new Map(okxData.map(candle => [candle[0], candle]));
  const lambooMap = new Map(lambooData.map(candle => [candle[0], candle]));

  const allTimestamps = new Set([...okxMap.keys(), ...lambooMap.keys()]);

  for (const timestamp of allTimestamps) {
    const okxCandle = okxMap.get(timestamp);
    const lambooCandle = lambooMap.get(timestamp);

    if (okxCandle || lambooCandle) {
      aligned.push({ timestamp, okx: okxCandle || null, lamboo: lambooCandle || null });
    }
  }

  return aligned.sort((a, b) => a.timestamp - b.timestamp);
}

function compareCandlesticks(okxCandle, lambooCandle) {
  if (!okxCandle || !lambooCandle) {
    return { priceDifferences: null, volumeDifference: null, status: 'missing_data' };
  }

  const priceDifferences = {
    open: calculatePercentageDifference(parseFloat(okxCandle[1]), parseFloat(lambooCandle[1])),
    high: calculatePercentageDifference(parseFloat(okxCandle[2]), parseFloat(lambooCandle[2])),
    low: calculatePercentageDifference(parseFloat(okxCandle[3]), parseFloat(lambooCandle[3])),
    close: calculatePercentageDifference(parseFloat(okxCandle[4]), parseFloat(lambooCandle[4]))
  };

  const volumeDifference = calculatePercentageDifference(okxCandle[5], lambooCandle[5]);

  return { priceDifferences, volumeDifference, status: 'compared' };
}

// =============== COMPARISON FUNCTION ===============
async function compareLambooOKX(token) {
  console.log(`\n🔍 So sánh ${token.symbol} (${token.name})`);
  console.log(`📍 Address: ${token.address}`);

  // Calculate rounded time window for consistent hourly boundaries
  const timeWindow = calculateRoundedTimeWindow5Hours();
  const { startTime, endTime } = timeWindow;

  console.log(`⏰ Thời gian phân tích: từ ${formatUTCTimestamp(startTime)} đến ${formatUTCTimestamp(endTime)}`);

  const errors = { okxErrors: [], lambooErrors: [] };
  let okxData = [], lambooData = [];

  try {
    console.log('📊 Đang lấy dữ liệu từ OKX API...');
    okxData = await fetchOKXData(token.address, timeWindow);
    console.log(`✅ OKX: Lấy được ${okxData.length} nến`);
  } catch (error) {
    errors.okxErrors.push(error.message);
    console.error(`❌ OKX Error: ${error.message}`);
  }

  try {
    console.log('📊 Đang lấy dữ liệu từ Lamboo V2 API...');
    lambooData = await fetchLambooV2Data(token.address, timeWindow);
    console.log(`✅ Lamboo V2: Lấy được ${lambooData.length} nến`);
  } catch (error) {
    errors.lambooErrors.push(error.message);
    console.error(`❌ Lamboo V2 Error: ${error.message}`);
  }

  if (okxData.length === 0 && lambooData.length === 0) {
    return {
      token,
      summary: { totalCandles: 0, matchingCandles: 0, averagePriceDifference: null, averageVolumeDifference: null },
      differences: [], errors, timeRange: timeWindow, significantDiscrepancies: []
    };
  }

  const alignedData = alignCandlestickData(okxData, lambooData);
  const differences = [];
  let totalPriceDifferences = { open: 0, high: 0, low: 0, close: 0 };
  let totalVolumeDifference = 0, matchingCandles = 0;
  const significantDiscrepancies = [];

  for (const aligned of alignedData) {
    const comparison = compareCandlesticks(aligned.okx, aligned.lamboo);
    const difference = {
      timestamp: new Date(aligned.timestamp * 1000).toISOString(),
      okx: aligned.okx, lamboo: aligned.lamboo, ...comparison
    };
    differences.push(difference);

    if (comparison.status === 'compared') {
      matchingCandles++;
      totalPriceDifferences.open += Math.abs(comparison.priceDifferences.open);
      totalPriceDifferences.high += Math.abs(comparison.priceDifferences.high);
      totalPriceDifferences.low += Math.abs(comparison.priceDifferences.low);
      totalPriceDifferences.close += Math.abs(comparison.priceDifferences.close);
      totalVolumeDifference += Math.abs(comparison.volumeDifference);

      // Check for significant discrepancies (>5% in any OHLC field)
      const maxPriceDiff = Math.max(
        Math.abs(comparison.priceDifferences.open),
        Math.abs(comparison.priceDifferences.high),
        Math.abs(comparison.priceDifferences.low),
        Math.abs(comparison.priceDifferences.close)
      );

      if (maxPriceDiff > 5) {
        significantDiscrepancies.push({
          ...difference,
          maxPriceDiff
        });
      }
    }
  }

  // Sort by highest price difference and limit to top 20
  significantDiscrepancies.sort((a, b) => b.maxPriceDiff - a.maxPriceDiff);
  const topDiscrepancies = significantDiscrepancies.slice(0, 20);

  const averagePriceDifference = matchingCandles > 0 ?
    (totalPriceDifferences.open + totalPriceDifferences.high +
     totalPriceDifferences.low + totalPriceDifferences.close) / (4 * matchingCandles) : null;

  const averageVolumeDifference = matchingCandles > 0 ? totalVolumeDifference / matchingCandles : null;

  console.log(`📈 Kết quả so sánh:`);
  console.log(`  - Tổng số nến: ${alignedData.length}`);
  console.log(`  - Nến khớp: ${matchingCandles}`);
  console.log(`  - Chênh lệch giá trung bình: ${averagePriceDifference?.toFixed(4)}%`);
  console.log(`  - Chênh lệch volume trung bình: ${averageVolumeDifference?.toFixed(2)}%`);

  return {
    token,
    summary: { totalCandles: alignedData.length, matchingCandles, averagePriceDifference, averageVolumeDifference },
    differences, errors, timeRange: timeWindow, significantDiscrepancies: topDiscrepancies
  };
}

// =============== HTML REPORT GENERATOR ===============
function generateHTMLReport(results) {
  const timestamp = new Date().toLocaleString();

  // Calculate time range from first result that has timeRange data
  let timeRangeText = '5 tiếng trước từ hiện tại';
  const firstResultWithTimeRange = results.find(r => r.timeRange);
  if (firstResultWithTimeRange) {
    const startTimeFormatted = formatUTCTimestamp(firstResultWithTimeRange.timeRange.startTime);
    const endTimeFormatted = formatUTCTimestamp(firstResultWithTimeRange.timeRange.endTime);
    timeRangeText = `từ ${startTimeFormatted} đến ${endTimeFormatted}`;
  }

  let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Lamboo V2 vs OKX OHLCV Comparison Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
            line-height: 1.6;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .summary h2 {
            margin-top: 0;
            font-size: 1.8em;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 0.9em;
        }
        th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.8em;
        }
        .token-info {
            text-align: left !important;
            max-width: 250px;
        }
        .token-name {
            font-weight: bold;
            font-size: 1.1em;
            color: #2c3e50;
            display: block;
            margin-bottom: 5px;
        }
        .token-symbol {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .token-address {
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            color: #95a5a6;
            word-break: break-all;
            margin-top: 5px;
        }
        .number {
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }
        .status {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .percentage {
            font-weight: bold;
        }
        .positive { color: #27ae60; }
        .negative { color: #e74c3c; }
        .neutral { color: #7f8c8d; }
        tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.001);
            transition: all 0.2s ease;
        }
        .error-section {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success-section {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .discrepancy-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #ecf0f1;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Lamboo V2 vs OKX OHLCV Comparison</h1>

        <div class="summary">
            <h2>📈 Tổng quan so sánh</h2>
            <p><strong>Thời gian tạo báo cáo:</strong> ${timestamp}</p>
            <p><strong>Khoảng thời gian phân tích:</strong> ${timeRangeText}</p>
            <p><strong>Timeframe:</strong> 1 phút (60 giây)</p>
            <p><strong>APIs so sánh:</strong> Lamboo V2 vs OKX (qua Lamboo gateway)</p>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">${results.length}</span>
                    <span class="stat-label">Tokens được phân tích</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${results.filter(r => r.summary.matchingCandles > 0).length}</span>
                    <span class="stat-label">So sánh thành công</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${results.reduce((sum, r) => sum + r.summary.totalCandles, 0)}</span>
                    <span class="stat-label">Tổng số nến</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${results.reduce((sum, r) => sum + r.summary.matchingCandles, 0)}</span>
                    <span class="stat-label">Nến khớp</span>
                </div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Token Info</th>
                    <th>Tổng nến</th>
                    <th>Nến khớp</th>
                    <th>Tỷ lệ khớp</th>
                    <th>Chênh lệch giá TB</th>
                    <th>Chênh lệch volume TB</th>
                    <th>Trạng thái</th>
                    <th>Errors</th>
                    <th>Chênh lệch >5%</th>
                </tr>
            </thead>
            <tbody>`;

  results.forEach(result => {
    const token = result.token;
    const summary = result.summary;
    const errors = result.errors;
    const discrepancies = result.significantDiscrepancies || [];

    const matchRate = summary.totalCandles > 0 ?
      (summary.matchingCandles / summary.totalCandles * 100).toFixed(1) : '0.0';

    const statusColor = getStatusColor(summary.averagePriceDifference, summary.averageVolumeDifference);
    const statusText = getStatusText(summary.averagePriceDifference, summary.averageVolumeDifference);

    const hasErrors = errors.okxErrors.length > 0 || errors.lambooErrors.length > 0;
    const errorCount = errors.okxErrors.length + errors.lambooErrors.length;

    html += `
                <tr style="background-color: ${statusColor};">
                    <td class="token-info">
                        <span class="token-name">${token.name}</span>
                        <span class="token-symbol">${token.symbol}</span>
                        <div class="token-address">${token.address}</div>
                    </td>
                    <td class="number">${formatNumber(summary.totalCandles)}</td>
                    <td class="number">${formatNumber(summary.matchingCandles)}</td>
                    <td class="number">${matchRate}%</td>
                    <td class="number percentage">${formatPercentage(summary.averagePriceDifference)}</td>
                    <td class="number percentage">${formatPercentage(summary.averageVolumeDifference)}</td>
                    <td><span class="status" style="background-color: ${statusColor};">${statusText}</span></td>
                    <td class="number ${hasErrors ? 'negative' : 'positive'}">${errorCount}</td>
                    <td class="number ${discrepancies.length > 0 ? 'negative' : 'positive'}">${discrepancies.length}</td>
                </tr>`;
  });

  html += `
            </tbody>
        </table>`;

  // Add discrepancy tables for each token that has significant discrepancies
  results.forEach(result => {
    const token = result.token;
    const discrepancies = result.significantDiscrepancies || [];

    if (discrepancies.length > 0) {
      html += `
        <div class="discrepancy-section">
          <h3>🔍 Chi tiết chênh lệch - ${token.symbol} (${token.name})</h3>
          ${generateDiscrepancyTable(discrepancies)}
        </div>`;
    }
  });

  html += `
        <div class="footer">
            <p>🔧 Generated by Lamboo OKX Comparison Tool</p>
            <p>📅 ${timestamp}</p>
            <p style="margin-top: 10px; font-size: 0.9em;">
              <strong>Chú thích:</strong> Chênh lệch >5% hiển thị các nến có sự khác biệt đáng kể về giá giữa 2 APIs.
              Màu xanh = Lamboo V2 cao hơn OKX, màu đỏ = Lamboo V2 thấp hơn OKX.
            </p>
        </div>
    </div>
</body>
</html>`;

  return html;
}

// =============== MAIN EXECUTION ===============
async function main() {
  console.log('🚀 Bắt đầu so sánh nâng cao Lamboo V2 vs OKX');
  console.log('📊 Phân tích tất cả tokens trong tokenList...');

  if (DEBUG_MODE) {
    console.log('🐛 DEBUG MODE ENABLED - API requests will be logged with cURL commands');
  }

  console.log('=' .repeat(60));
  
  const results = [];
  const activeTokens = tokenData.filter(token => token && token.address && token.symbol);
  
  if (activeTokens.length === 0) {
    console.error('❌ Không tìm thấy token nào trong tokenList');
    return;
  }
  
  console.log(`📋 Tìm thấy ${activeTokens.length} token(s) để phân tích`);
  
  for (let i = 0; i < activeTokens.length; i++) {
    const token = activeTokens[i];
    
    console.log(`\n⏳ Đang xử lý ${i + 1}/${activeTokens.length}: ${token.symbol}`);
    
    try {
      const result = await compareLambooOKX(token);
      results.push(result);
      
      // Delay giữa các requests để tránh rate limit
      if (i < activeTokens.length - 1) {
        console.log('⏱️  Chờ 2 giây trước khi xử lý token tiếp theo...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error) {
      console.error(`❌ Lỗi khi xử lý ${token.symbol}: ${error.message}`);
      results.push({
        token,
        summary: {
          totalCandles: 0,
          matchingCandles: 0,
          averagePriceDifference: null,
          averageVolumeDifference: null
        },
        differences: [],
        errors: {
          okxErrors: [error.message],
          lambooErrors: []
        }
      });
    }
  }
  
  // Generate reports
  console.log('\n📊 Tạo báo cáo...');
  
  // Save JSON report
  const jsonPath = path.join(__dirname, '..', 'outputs', 'json', `lamboo_okx_1m.json`);
  const jsonReport = {
    generatedAt: new Date().toISOString(),
    timeframe: '1m',
    period: '5 hours',
    totalTokens: results.length,
    results
  };

  // Ensure output directory exists
  const jsonDir = path.dirname(jsonPath);
  if (!fs.existsSync(jsonDir)) {
    fs.mkdirSync(jsonDir, { recursive: true });
  }
  
  fs.writeFileSync(jsonPath, JSON.stringify(jsonReport, null, 2));
  console.log(`💾 Đã lưu báo cáo JSON: ${jsonPath}`);
  
  // Generate HTML report
  const htmlReport = generateHTMLReport(results);
  const htmlPath = path.join(__dirname, '..', 'outputs', 'html', `lamboo_okx_1m.html`);

  // Ensure output directory exists
  const htmlDir = path.dirname(htmlPath);
  if (!fs.existsSync(htmlDir)) {
    fs.mkdirSync(htmlDir, { recursive: true });
  }
  
  fs.writeFileSync(htmlPath, htmlReport);
  console.log(`📊 Đã tạo báo cáo HTML: ${htmlPath}`);
  
  // Summary statistics
  const successfulComparisons = results.filter(r => r.summary.matchingCandles > 0);
  const totalCandles = results.reduce((sum, r) => sum + r.summary.totalCandles, 0);
  const totalMatchingCandles = results.reduce((sum, r) => sum + r.summary.matchingCandles, 0);
  
  console.log('\n' + '='.repeat(60));
  console.log('📈 TỔNG KẾT:');
  console.log(`  📊 Tổng số tokens: ${results.length}`);
  console.log(`  ✅ So sánh thành công: ${successfulComparisons.length}`);
  console.log(`  ❌ Thất bại: ${results.length - successfulComparisons.length}`);
  console.log(`  🕯️  Tổng số nến: ${totalCandles.toLocaleString()}`);
  console.log(`  🎯 Nến khớp: ${totalMatchingCandles.toLocaleString()}`);
  console.log(`  📊 Tỷ lệ khớp: ${totalCandles > 0 ? (totalMatchingCandles / totalCandles * 100).toFixed(2) : 0}%`);
  
  if (successfulComparisons.length > 0) {
    const avgPriceDiffs = successfulComparisons
      .map(r => r.summary.averagePriceDifference)
      .filter(d => d !== null);
    
    const avgVolDiffs = successfulComparisons
      .map(r => r.summary.averageVolumeDifference)
      .filter(d => d !== null);
    
    if (avgPriceDiffs.length > 0) {
      const overallAvgPriceDiff = avgPriceDiffs.reduce((sum, d) => sum + Math.abs(d), 0) / avgPriceDiffs.length;
      console.log(`  💰 Chênh lệch giá trung bình: ${overallAvgPriceDiff.toFixed(4)}%`);
    }
    
    if (avgVolDiffs.length > 0) {
      const overallAvgVolDiff = avgVolDiffs.reduce((sum, d) => sum + Math.abs(d), 0) / avgVolDiffs.length;
      console.log(`  📊 Chênh lệch volume trung bình: ${overallAvgVolDiff.toFixed(2)}%`);
    }
  }
  
  console.log('\n✅ Hoàn thành tất cả so sánh!');
}

// Run if executed directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
