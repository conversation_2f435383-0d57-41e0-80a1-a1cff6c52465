#!/usr/bin/env node

/**
 * Earnium API Client
 * Fetches pool exploration data from Earnium API and saves decoded response to JSON
 * 
 * Usage: node pool/earnium-api-client.js
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// =============== CONFIGURATION ===============
const API_CONFIG = {
  baseUrl: 'https://api.earnium.io/api/v1/pool/list-pool-explore',
  method: 'POST',
  params: {
    page: 1,
    limit: 20,
    order: 'desc',
    sort_by: 'tvl',
    search: '0xd5832c6ebc4d479c883c07541deb8fb2236d27b5976243df9520df0ced3bcd97',
    stable: null
  },
  headers: {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Content-Type': 'application/json',
    'Origin': 'https://earnium.io',
    'Referer': 'https://earnium.io/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    // Add potential authentication headers (may need to be obtained from browser)
    'Authorization': process.env.EARNIUM_AUTH_TOKEN || '',
    'X-API-Key': process.env.EARNIUM_API_KEY || '',
    'Cookie': process.env.EARNIUM_COOKIES || ''
  },
  timeout: 30000
};

// Alternative configurations to try if main fails
const FALLBACK_CONFIGS = [
  {
    url: 'https://api.earnium.io/api/v1/pool/list-pool-explore',
    method: 'GET',
    params: API_CONFIG.params,
    headers: {
      ...API_CONFIG.headers,
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    timeout: API_CONFIG.timeout
  },
  {
    url: 'https://earnium.io/api/v1/pool/list-pool-explore',
    method: 'POST',
    params: API_CONFIG.params,
    headers: {
      ...API_CONFIG.headers,
      'sec-fetch-site': 'same-origin'
    },
    timeout: API_CONFIG.timeout
  }
];

const OUTPUT_DIR = path.join(__dirname, 'outputs');
const OUTPUT_FILE = path.join(OUTPUT_DIR, 'earnium-pool-data.json');

// Debug mode
const DEBUG_MODE = process.env.EARNIUM_DEBUG === 'true' || process.argv.includes('--debug');

// =============== UTILITY FUNCTIONS ===============
function logInfo(message) {
  console.log(`🔵 [INFO] ${message}`);
}

function logSuccess(message) {
  console.log(`✅ [SUCCESS] ${message}`);
}

function logError(message) {
  console.log(`❌ [ERROR] ${message}`);
}

function logDebug(message) {
  if (DEBUG_MODE) {
    console.log(`🐛 [DEBUG] ${message}`);
  }
}

function logApiRequest(config) {
  if (DEBUG_MODE) {
    console.log('\n🔍 [API Request Debug]:');
    console.log('=' .repeat(80));
    console.log(`📡 Method: ${config.method}`);
    console.log(`🌐 URL: ${config.url}`);
    console.log(`🔗 Query Params: ${JSON.stringify(config.params, null, 2)}`);
    console.log(`📋 Headers: ${JSON.stringify(config.headers, null, 2)}`);
    
    // Generate cURL command for manual testing
    const curlHeaders = Object.entries(config.headers)
      .map(([key, value]) => `-H "${key}: ${value}"`)
      .join(' \\\n  ');
    
    const queryString = new URLSearchParams(config.params).toString();
    const fullUrl = `${config.url}?${queryString}`;
    
    console.log('\n💻 cURL Command:');
    console.log(`curl -X ${config.method} "${fullUrl}" \\`);
    console.log(`  ${curlHeaders}`);
    console.log('=' .repeat(80));
  }
}

// =============== DECODING FUNCTIONS ===============
function detectEncodingType(data) {
  const dataStr = typeof data === 'string' ? data : JSON.stringify(data);
  
  // Check for common encoding patterns
  if (dataStr.includes('\\u')) {
    return 'unicode-escape';
  }
  if (dataStr.match(/^[A-Za-z0-9+/]+=*$/)) {
    return 'base64';
  }
  if (dataStr.match(/^[0-9a-fA-F]+$/)) {
    return 'hex';
  }
  if (dataStr.includes('%')) {
    return 'url-encoded';
  }
  
  return 'unknown';
}

function decodeUnicodeEscape(str) {
  try {
    return str.replace(/\\u[\dA-F]{4}/gi, (match) => {
      return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
    });
  } catch (error) {
    logDebug(`Unicode escape decode failed: ${error.message}`);
    return str;
  }
}

function decodeBase64(str) {
  try {
    return Buffer.from(str, 'base64').toString('utf8');
  } catch (error) {
    logDebug(`Base64 decode failed: ${error.message}`);
    return str;
  }
}

function decodeHex(str) {
  try {
    return Buffer.from(str, 'hex').toString('utf8');
  } catch (error) {
    logDebug(`Hex decode failed: ${error.message}`);
    return str;
  }
}

function decodeUrlEncoded(str) {
  try {
    return decodeURIComponent(str);
  } catch (error) {
    logDebug(`URL decode failed: ${error.message}`);
    return str;
  }
}

function attemptDecode(data) {
  logDebug('Attempting to decode response data...');
  
  let decodedData = data;
  const originalStr = typeof data === 'string' ? data : JSON.stringify(data);
  
  // Try multiple decoding approaches
  const decodingMethods = [
    { name: 'Unicode Escape', func: decodeUnicodeEscape },
    { name: 'Base64', func: decodeBase64 },
    { name: 'Hex', func: decodeHex },
    { name: 'URL Encoded', func: decodeUrlEncoded }
  ];
  
  for (const method of decodingMethods) {
    try {
      const encodingType = detectEncodingType(originalStr);
      logDebug(`Detected encoding type: ${encodingType}`);
      
      if (encodingType === method.name.toLowerCase().replace(' ', '-')) {
        logDebug(`Trying ${method.name} decoding...`);
        const decoded = method.func(originalStr);
        
        if (decoded !== originalStr) {
          logDebug(`${method.name} decoding successful`);
          
          // Try to parse as JSON if it looks like JSON
          if (decoded.trim().startsWith('{') || decoded.trim().startsWith('[')) {
            try {
              decodedData = JSON.parse(decoded);
              logSuccess(`Successfully decoded using ${method.name} and parsed as JSON`);
              break;
            } catch (jsonError) {
              logDebug(`JSON parse failed after ${method.name} decode: ${jsonError.message}`);
              decodedData = decoded;
            }
          } else {
            decodedData = decoded;
          }
        }
      }
    } catch (error) {
      logDebug(`${method.name} decoding failed: ${error.message}`);
    }
  }
  
  return decodedData;
}

// =============== API CLIENT FUNCTIONS ===============
async function tryApiRequest(config, configName = 'main') {
  try {
    logInfo(`Trying ${configName} configuration...`);
    logApiRequest(config);

    const response = await axios(config);

    logDebug(`Response Status: ${response.status}`);
    logDebug(`Response Headers: ${JSON.stringify(response.headers, null, 2)}`);
    logDebug(`Response Data Length: ${JSON.stringify(response.data).length} characters`);

    if (DEBUG_MODE) {
      const dataPreview = JSON.stringify(response.data).substring(0, 500);
      logDebug(`Response Preview: ${dataPreview}...`);
    }

    // Check if response looks like an error page
    const dataStr = typeof response.data === 'string' ? response.data : JSON.stringify(response.data);
    if (dataStr.includes('Access Denied') || dataStr.includes('401') || dataStr.includes('<!DOCTYPE html>')) {
      throw new Error(`API returned error page instead of data (Status: ${response.status})`);
    }

    return response.data;

  } catch (error) {
    if (error.response) {
      logError(`${configName} config failed with status ${error.response.status}: ${error.response.statusText}`);
      if (DEBUG_MODE) {
        const errorData = typeof error.response.data === 'string'
          ? error.response.data.substring(0, 200)
          : JSON.stringify(error.response.data).substring(0, 200);
        logDebug(`Error response preview: ${errorData}...`);
      }
    } else if (error.request) {
      logError(`${configName} config network error: No response received from API`);
    } else {
      logError(`${configName} config setup error: ${error.message}`);
    }
    throw error;
  }
}

async function fetchEarniumPoolData() {
  const configs = [
    { config: {
      method: API_CONFIG.method,
      url: API_CONFIG.baseUrl,
      params: API_CONFIG.params,
      headers: API_CONFIG.headers,
      timeout: API_CONFIG.timeout,
      validateStatus: function (status) {
        return status >= 200 && status < 500; // Accept wider range to detect error pages
      }
    }, name: 'main' },
    ...FALLBACK_CONFIGS.map((config, index) => ({
      config: {
        ...config,
        validateStatus: function (status) {
          return status >= 200 && status < 500;
        }
      },
      name: `fallback-${index + 1}`
    }))
  ];

  let lastError;

  for (const { config, name } of configs) {
    try {
      const data = await tryApiRequest(config, name);
      logSuccess(`Successfully fetched data using ${name} configuration`);
      return data;
    } catch (error) {
      lastError = error;
      logError(`${name} configuration failed, trying next...`);
    }
  }

  logError('All API configurations failed');
  throw lastError;
}

function ensureOutputDirectory() {
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    logDebug(`Created output directory: ${OUTPUT_DIR}`);
  }
}

function analyzeApiResponse(data) {
  const dataStr = typeof data === 'string' ? data : JSON.stringify(data);

  const analysis = {
    isErrorPage: false,
    errorType: null,
    isEncoded: false,
    encodingType: null,
    hasJsonStructure: false,
    dataSize: dataStr.length
  };

  // Check for error pages
  if (dataStr.includes('<!DOCTYPE html>')) {
    analysis.isErrorPage = true;

    if (dataStr.includes('Access Denied') || dataStr.includes('401')) {
      analysis.errorType = 'access_denied';
    } else if (dataStr.includes('404') || dataStr.includes('Not Found')) {
      analysis.errorType = 'not_found';
    } else if (dataStr.includes('500') || dataStr.includes('Internal Server Error')) {
      analysis.errorType = 'server_error';
    } else {
      analysis.errorType = 'unknown_html_error';
    }
  }

  // Check for JSON structure
  if (dataStr.trim().startsWith('{') || dataStr.trim().startsWith('[')) {
    analysis.hasJsonStructure = true;
  }

  // Check for encoding
  analysis.encodingType = detectEncodingType(dataStr);
  analysis.isEncoded = analysis.encodingType !== 'unknown';

  return analysis;
}

function saveDecodedData(data, analysis = null) {
  try {
    ensureOutputDirectory();

    const jsonData = {
      fetchedAt: new Date().toISOString(),
      apiEndpoint: API_CONFIG.baseUrl,
      queryParams: API_CONFIG.params,
      responseAnalysis: analysis || analyzeApiResponse(data),
      decodedData: data
    };

    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(jsonData, null, 2));
    logSuccess(`Data saved to: ${OUTPUT_FILE}`);

    if (jsonData.responseAnalysis.isErrorPage) {
      logError(`⚠️  Response appears to be an error page (${jsonData.responseAnalysis.errorType})`);

      if (jsonData.responseAnalysis.errorType === 'access_denied') {
        console.log('\n🚨 ACCESS DENIED DETECTED:');
        console.log('   • The API returned a 401 "Access Denied" error');
        console.log('   • This may be due to:');
        console.log('     - Geographic restrictions (VPN may be required)');
        console.log('     - Missing authentication tokens');
        console.log('     - API endpoint changes');
        console.log('     - Rate limiting or IP blocking');
        console.log('\n💡 SUGGESTED SOLUTIONS:');
        console.log('   1. Try using a VPN to change your location');
        console.log('   2. Check if authentication tokens are required');
        console.log('   3. Verify the API endpoint is still valid');
        console.log('   4. Contact Earnium support for API access');
      }
    }

    return OUTPUT_FILE;
  } catch (error) {
    logError(`Failed to save data: ${error.message}`);
    throw error;
  }
}

// =============== MAIN EXECUTION ===============
async function main() {
  try {
    console.log('🚀 Earnium API Client');
    console.log('=' .repeat(50));

    if (DEBUG_MODE) {
      console.log('🐛 DEBUG MODE ENABLED - Detailed logging active');
    }

    let rawData;
    let isErrorResponse = false;

    try {
      // Fetch data from API
      rawData = await fetchEarniumPoolData();
    } catch (error) {
      // Check if this is an API error that we can still analyze
      if (error.message.includes('API returned error page')) {
        logError('API returned error page, but continuing with analysis...');
        // Try to get the error response data from the first successful HTTP request
        // This is a bit hacky, but we need to get the HTML error page for analysis
        try {
          const config = {
            method: API_CONFIG.method,
            url: API_CONFIG.baseUrl,
            params: API_CONFIG.params,
            headers: API_CONFIG.headers,
            timeout: API_CONFIG.timeout,
            validateStatus: () => true // Accept any status code
          };
          const response = await axios(config);
          rawData = response.data;
          isErrorResponse = true;
        } catch (fallbackError) {
          throw error; // Re-throw original error if fallback fails
        }
      } else {
        throw error; // Re-throw non-API errors
      }
    }

    // Analyze the response
    logInfo('Analyzing API response...');
    const analysis = analyzeApiResponse(rawData);

    if (analysis.isErrorPage || isErrorResponse) {
      logError(`API returned an error page: ${analysis.errorType}`);
      // Save the error response for debugging
      const outputPath = saveDecodedData(rawData, analysis);

      // Display error summary
      console.log('\n' + '=' .repeat(50));
      console.log('⚠️  ERROR RESPONSE SUMMARY:');
      console.log(`   • API Endpoint: ${API_CONFIG.baseUrl}`);
      console.log(`   • Error Type: ${analysis.errorType}`);
      console.log(`   • Response Size: ${analysis.dataSize} characters`);
      console.log(`   • Output File: ${path.basename(outputPath)}`);
      console.log('=' .repeat(50));

      return; // Exit early for error pages
    }

    // Attempt to decode the response
    logInfo('Processing and decoding API response...');
    const decodedData = attemptDecode(rawData);

    // Save decoded data to file
    const outputPath = saveDecodedData(decodedData, analysis);

    // Display success summary
    console.log('\n' + '=' .repeat(50));
    console.log('📊 SUCCESS SUMMARY:');
    console.log(`   • API Endpoint: ${API_CONFIG.baseUrl}`);
    console.log(`   • Query Parameters: ${Object.keys(API_CONFIG.params).length} params`);
    console.log(`   • Output File: ${path.basename(outputPath)}`);
    console.log(`   • Data Type: ${typeof decodedData}`);

    if (typeof decodedData === 'object' && decodedData !== null) {
      const keys = Object.keys(decodedData);
      console.log(`   • Object Keys: ${keys.length > 0 ? keys.join(', ') : 'none'}`);
    }

    console.log('=' .repeat(50));
    logSuccess('Earnium API data fetch and decode completed successfully!');

  } catch (error) {
    logError(`Script execution failed: ${error.message}`);
    if (DEBUG_MODE) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// =============== ERROR HANDLING ===============
process.on('uncaughtException', (error) => {
  logError(`Uncaught exception: ${error.message}`);
  console.error(error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

// =============== SCRIPT ENTRY POINT ===============
if (require.main === module) {
  main().catch((error) => {
    logError(`Main execution failed: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  });
}

module.exports = {
  fetchEarniumPoolData,
  attemptDecode,
  saveDecodedData,
  API_CONFIG
};
