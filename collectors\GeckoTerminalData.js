/**
 * Fetch token data from GeckoTerminal API for tokens in tokenList.js
 * Simple one-command script: just run `node GeckoTerminalData.js`
 * 
 * Process:
 * 1. Search for token pairs using: https://app.geckoterminal.com/api/p1/search?query={AddressToken}
 * 2. Get detailed pool data using: https://app.geckoterminal.com/api/p1/aptos/pools/{pairAddress}
 * 
 * Output: address, name, historical data for 5m, 1h, 6h, 24h timeframes
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Import token list
const { tokenData } = require('../tokenList.js');

// === CONFIG ===
const CONFIG = {
  NETWORK: 'aptos',
  MAX_TOKENS: 30, // Maximum tokens to process (set to -1 for all tokens)
  MAX_POOLS_PER_TOKEN: 30, // Maximum pools to fetch per token
  OUT_FILE: '../results/GeckoTerminalData.json',
  DELAY_BETWEEN_REQUESTS: 1000, // ms delay between API calls
  DELAY_BETWEEN_POOLS: 500, // ms delay between pool detail calls
  TIMEOUT: 30000, // Request timeout in ms
};

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

async function searchTokenPools(tokenAddress, tokenInfo) {
  const url = `https://app.geckoterminal.com/api/p1/search?query=${encodeURIComponent(tokenAddress)}`;
  
  const headers = {
    'accept': 'application/json',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'referer': 'https://www.geckoterminal.com/',
  };

  try {
    console.log(`  Searching pools for ${tokenInfo.symbol}...`);
    const res = await axios.get(url, { headers, timeout: CONFIG.TIMEOUT });
    
    if (!res.data?.data?.attributes?.pools) {
      console.log(`    No pools found for ${tokenInfo.symbol}`);
      return [];
    }

    const pools = res.data.data.attributes.pools;
    console.log(`    Found ${pools.length} pool(s)`);
    
    // Filter pools for Aptos network and return pool addresses
    const aptosPools = pools
      .filter(pool => pool.network?.identifier === CONFIG.NETWORK)
      .slice(0, CONFIG.MAX_POOLS_PER_TOKEN)
      .map(pool => ({
        address: pool.address,
        dex: pool.dex?.name || 'Unknown',
        name: pool.tokens?.map(t => t.symbol).join(' / ') || 'Unknown',
        priceUsd: parseFloat(pool.price_in_usd) || 0,
        reserveUsd: parseFloat(pool.reserve_in_usd) || 0,
        volumeUsd: parseFloat(pool.from_volume_in_usd) || 0
      }));

    console.log(`    Found ${aptosPools.length} Aptos pool(s)`);
    return aptosPools;
    
  } catch (err) {
    const status = err?.response?.status;
    const message = err?.response?.data?.message || err.message;
    console.error(`    Error searching pools for ${tokenInfo.symbol} (${status}): ${message}`);
    return [];
  }
}

async function getPoolDetails(poolAddress, tokenInfo) {
  const url = `https://app.geckoterminal.com/api/p1/${CONFIG.NETWORK}/pools/${encodeURIComponent(poolAddress)}?include=dex%2Cdex.network.explorers%2Cdex_link_services%2Cnetwork_link_services%2Cpairs%2Ctoken_link_services%2Ctag_link_services%2Ctokens.token_security_metric%2Ctokens.tags%2Cpool_locked_liquidities&base_token=0`;
  
  const headers = {
    'accept': 'application/json',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'referer': 'https://www.geckoterminal.com/',
  };

  try {
    console.log(`    Getting details for pool ${poolAddress.substring(0, 10)}...`);
    const res = await axios.get(url, { headers, timeout: CONFIG.TIMEOUT });
    
    if (!res.data?.data?.attributes) {
      console.log(`      No data found for pool ${poolAddress.substring(0, 10)}...`);
      return null;
    }

    const attrs = res.data.data.attributes;
    const historical = attrs.historical_data || {};
    
    // Extract data for required timeframes
    const result = {
      address: attrs.address,
      name: attrs.name,
      priceUsd: parseFloat(attrs.price_in_usd) || 0,
      reserveUsd: parseFloat(attrs.reserve_in_usd) || 0,
      fdv: parseFloat(attrs.fully_diluted_valuation) || 0,
      swapCount24h: attrs.swap_count_24h || 0,
      data: {
        '5m': historical.last_5m ? {
          swaps: historical.last_5m.swaps_count || 0,
          buyers: historical.last_5m.buyers_count || 0,
          sellers: historical.last_5m.sellers_count || 0,
          volume: parseFloat(historical.last_5m.volume_in_usd) || 0,
          buySwaps: historical.last_5m.buy_swaps_count || 0,
          sellSwaps: historical.last_5m.sell_swaps_count || 0,
          priceUsd: parseFloat(historical.last_5m.price_in_usd) || 0
        } : null,
        '1h': historical.last_1h ? {
          swaps: historical.last_1h.swaps_count || 0,
          buyers: historical.last_1h.buyers_count || 0,
          sellers: historical.last_1h.sellers_count || 0,
          volume: parseFloat(historical.last_1h.volume_in_usd) || 0,
          buySwaps: historical.last_1h.buy_swaps_count || 0,
          sellSwaps: historical.last_1h.sell_swaps_count || 0,
          priceUsd: parseFloat(historical.last_1h.price_in_usd) || 0
        } : null,
        '6h': historical.last_6h ? {
          swaps: historical.last_6h.swaps_count || 0,
          buyers: historical.last_6h.buyers_count || 0,
          sellers: historical.last_6h.sellers_count || 0,
          volume: parseFloat(historical.last_6h.volume_in_usd) || 0,
          buySwaps: historical.last_6h.buy_swaps_count || 0,
          sellSwaps: historical.last_6h.sell_swaps_count || 0,
          priceUsd: parseFloat(historical.last_6h.price_in_usd) || 0
        } : null,
        '24h': historical.last_24h ? {
          swaps: historical.last_24h.swaps_count || 0,
          buyers: historical.last_24h.buyers_count || 0,
          sellers: historical.last_24h.sellers_count || 0,
          volume: parseFloat(historical.last_24h.volume_in_usd) || 0,
          buySwaps: historical.last_24h.buy_swaps_count || 0,
          sellSwaps: historical.last_24h.sell_swaps_count || 0,
          priceUsd: parseFloat(historical.last_24h.price_in_usd) || 0
        } : null
      },
      // Price changes
      priceChanges: attrs.price_percent_changes || {},
      // Source token info
      sourceToken: {
        address: tokenInfo.address,
        symbol: tokenInfo.symbol,
        name: tokenInfo.name
      }
    };

    const vol24h = result.data['24h']?.volume || 0;
    console.log(`      ✅ Pool details: Vol24h=$${vol24h.toLocaleString()}`);
    return result;
    
  } catch (err) {
    const status = err?.response?.status;
    const message = err?.response?.data?.message || err.message;
    console.error(`      Error getting pool details (${status}): ${message}`);
    return null;
  }
}

async function main() {
  console.log(`Đang xử lý ${tokenData.length} token từ tokenList.js...`);
  
  const tokensToProcess = CONFIG.MAX_TOKENS === -1 ? tokenData : tokenData.slice(0, CONFIG.MAX_TOKENS);
  console.log(`Sẽ xử lý ${tokensToProcess.length} token đầu tiên.`);

  const allResults = [];
  
  for (let tokenIndex = 0; tokenIndex < tokensToProcess.length; tokenIndex++) {
    const token = tokensToProcess[tokenIndex];
    console.log(`\n[${tokenIndex + 1}/${tokensToProcess.length}] Xử lý token: ${token.symbol} (${token.name})`);
    console.log(`Token address: ${token.address}`);
    
    try {
      // Step 1: Search for pools
      const pools = await searchTokenPools(token.address, token);
      
      if (pools.length === 0) {
        console.log(`  Không tìm thấy pool nào cho token ${token.symbol}`);
        continue;
      }
      
      // Step 2: Get details for each pool
      for (let poolIndex = 0; poolIndex < pools.length; poolIndex++) {
        const pool = pools[poolIndex];
        console.log(`  [${poolIndex + 1}/${pools.length}] Pool: ${pool.name} (${pool.dex})`);
        
        const poolDetails = await getPoolDetails(pool.address, token);
        
        if (poolDetails) {
          allResults.push(poolDetails);
        }
        
        // Delay between pool detail calls
        if (poolIndex < pools.length - 1) {
          await sleep(CONFIG.DELAY_BETWEEN_POOLS);
        }
      }
      
    } catch (error) {
      console.error(`  ❌ Lỗi khi xử lý token ${token.symbol}:`, error.message);
    }
    
    // Delay between tokens
    if (tokenIndex < tokensToProcess.length - 1) {
      console.log(`  Đợi ${CONFIG.DELAY_BETWEEN_REQUESTS}ms...`);
      await sleep(CONFIG.DELAY_BETWEEN_REQUESTS);
    }
  }

  // Save results
  const outputPath = path.resolve(__dirname, CONFIG.OUT_FILE);
  fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2));
  console.log(`\n✅ Đã lưu ${allResults.length} pool record vào ${outputPath}`);
  
  // Summary by token
  const summary = {};
  allResults.forEach(record => {
    const symbol = record.sourceToken.symbol;
    if (!summary[symbol]) summary[symbol] = 0;
    summary[symbol]++;
  });
  
  console.log('\n📊 Tóm tắt kết quả:');
  Object.entries(summary).forEach(([symbol, count]) => {
    console.log(`  ${symbol}: ${count} pool(s)`);
  });

  // Summary statistics for 24h data
  const validResults = allResults.filter(r => r.data['24h']);
  if (validResults.length > 0) {
    const totalVolume24h = validResults.reduce((sum, r) => sum + (r.data['24h']?.volume || 0), 0);
    const totalSwaps24h = validResults.reduce((sum, r) => sum + (r.data['24h']?.swaps || 0), 0);
    const avgPrice = validResults.reduce((sum, r) => sum + r.priceUsd, 0) / validResults.length;
    
    console.log('\n📈 Thống kê tổng hợp 24h:');
    console.log(`  Pools có dữ liệu: ${validResults.length}/${allResults.length}`);
    console.log(`  Tổng Volume: $${totalVolume24h.toLocaleString()}`);
    console.log(`  Tổng Swaps: ${totalSwaps24h.toLocaleString()}`);
    console.log(`  Giá trung bình: $${avgPrice.toFixed(6)}`);
  }
}

main();
